const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const { v4: uuidv4 } = require('uuid'); // Per generare ID di gioco unici
const cookieParser = require('cookie-parser');
const cors = require('cors');
const helmet = require('helmet');
const jwt = require('jsonwebtoken');
const fs = require('fs');

// Verifica che il modulo Board.js sia caricato correttamente
const Board = require('../game_logic/Board');
// console.log('Caricato modulo Board.js con isCornerPosition:', typeof Board.prototype.isCornerPosition === 'function');

const Game = require('../game_logic/Game');
const { getAiMove } = require('../ai/index');

// Importa le route
const authRoutes = require('./routes/auth');
const multiplayerRoutes = require('./routes/multiplayer');
const User = require('./database/models/User');

// Importa i controller del multiplayer
const gameController = require('./controllers/multiplayer/gameController');
const matchmakingController = require('./controllers/multiplayer/matchmakingController');
const ratingController = require('./controllers/multiplayer/ratingController');
const GameSession = require('./models/multiplayer/GameSession');
const GameMove = require('./models/multiplayer/GameMove');
const MatchmakingQueue = require('./models/multiplayer/MatchmakingQueue');
const { createMultiplayerTables } = require('./database/migrations/create_multiplayer_tables');
const { addMoveNumberColumn } = require('./database/migrations/add_move_number_column');

// Importa i servizi
const notificationService = require('./services/notificationService');
const cacheService = require('./services/cacheService');

const app = express();
const server = http.createServer(app);
// Aumenta maxHttpBufferSize per gestire stati di gioco potenzialmente grandi
const io = socketIo(server, {
  maxHttpBufferSize: 1e8, // 100 MB (esempio, regola secondo necessità)
  cors: {
    origin: "*",
    methods: ["GET", "POST"],
    credentials: true
  }
});

// Inizializza il gameController con l'istanza Socket.IO
gameController.initialize(io);
console.log('[SERVER INIT] gameController inizializzato con Socket.IO per emettere eventi gameOver');

// Configurazione JWT
const JWT_SECRET = process.env.JWT_SECRET || 'skemino_secret_key';

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(cookieParser());
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(helmet({
  contentSecurityPolicy: false, // Disabilitato per debug
}));

const PORT = process.env.PORT || 3000;

// --- Gestione Giochi Locali ---
// NOTA: Questi oggetti sono solo per le partite locali. Le partite online sono gestite dal gameController
const activeGames = {}; // { gameId: Game instance } - Solo per partite locali/AI
const playerGameMap = {}; // { socketId: gameId } - Solo per partite locali/AI
const userGameMap = {}; // { userId: gameId } - Mappa per gestire riconnessioni per partite online
let waitingPlayer = null; // Socket ID del giocatore in attesa (per modalità vs Player)

// Il gameController ora gestisce internamente le sue mappe
console.log('[SERVER INIT] gameController pronto per gestire le partite online');

// Sistema di persistenza dei rating (semplificato, in produzione usare un database)
const playerRatings = {}; // { playerId: { rating: number, playerName: string } }

// Serve i file statici dalla directory 'client'
const clientPath = path.join(__dirname, '..', 'client');
app.use(express.static(clientPath));

// Route principale per servire l'index.html
app.get('/', (req, res) => {
  res.sendFile(path.join(clientPath, 'index.html'));
});

// Route per la home page degli utenti autenticati
app.get('/home', (req, res) => {
  res.sendFile(path.join(clientPath, 'home-logged.html'));
});

// Route per la pagina di gioco
app.get('/game', (req, res) => {
  res.sendFile(path.join(clientPath, 'game.html'));
});

// Route per le pagine di autenticazione
app.get('/login', (req, res) => {
  res.sendFile(path.join(clientPath, 'login.html'));
});

app.get('/register', (req, res) => {
  res.sendFile(path.join(clientPath, 'register.html'));
});

app.get('/forgot-password', (req, res) => {
  res.sendFile(path.join(clientPath, 'forgot-password.html'));
});

app.get('/reset-password/:token', (req, res) => {
  res.sendFile(path.join(clientPath, 'reset-password.html'));
});

app.get('/activate-account', (req, res) => {
  res.sendFile(path.join(clientPath, 'activate-account.html'));
});

// Route per la pagina di gioco (no-logged.html con permalink /play)
app.get('/play', (req, res) => {
  res.sendFile(path.join(clientPath, 'no-logged.html'));
});

app.get('/simple-register', (req, res) => {
  res.sendFile(path.join(clientPath, 'simple-register.html'));
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/multiplayer', multiplayerRoutes);

// API per ottenere la classifica dei giocatori
app.get('/api/classifica', (req, res) => {
  // Ottieni i top 10 giocatori dal database
  const topPlayers = User.getTopRanked(10);
  res.json(topPlayers);
});

// --- Funzioni per la gestione dei rating ---

/**
 * Ottiene la classifica di tutti i giocatori con i loro rating
 * @returns {Array} Array di oggetti giocatore ordinati per rating decrescente
 */
function getAllPlayersRating() {
  // Raccogli i rating dai giochi attivi e dalla memoria persistente
  let allRatings = { ...playerRatings };

  // Aggiungi i rating dai giochi attivi che potrebbero non essere ancora stati persistiti
  Object.values(activeGames).forEach(game => {
    for (const [playerId, rating] of Object.entries(game.playerRatings)) {
      if (playerId !== 'ai_player' && game.playerNames[playerId]) {
        allRatings[playerId] = {
          rating: rating,
          playerName: game.playerNames[playerId],
          level: game.getPlayerLevel(playerId)
        };
      }
    }
  });

  // Converti in array e ordina per rating decrescente
  const sortedRatings = Object.entries(allRatings)
    .filter(([id, data]) => id !== 'ai_player' && data.playerName) // Filtra l'AI e i giocatori senza nome
    .map(([id, data]) => ({
      id,
      name: data.playerName,
      rating: data.rating,
      level: data.level || getPlayerLevel(data.rating) // Fallback per oggetti senza livello
    }))
    .sort((a, b) => b.rating - a.rating); // Ordina per rating decrescente

  return sortedRatings;
}

/**
 * Funzione di fallback per calcolare il livello di un giocatore
 * @param {number} rating Rating del giocatore
 * @returns {Object} Informazioni sul livello
 */
function getPlayerLevel(rating) {
  const RATING_LEVELS = Game.RATING_LEVELS;

  for (const level of RATING_LEVELS) {
    if (rating >= level.range[0] && rating <= level.range[1]) {
      return {
        name: level.name,
        avatar: level.avatar,
        min: level.range[0],
        max: level.range[1],
        progress: (rating - level.range[0]) / (level.range[1] - level.range[0])
      };
    }
  }

  return null;
}

// Middleware per autenticare le connessioni WebSocket
io.use((socket, next) => {
  try {
            // Log di connessione ridotto
        console.log(`New socket connection: ${socket.id}`);

    const token = socket.handshake.auth.token || socket.handshake.query.token;

            // Token trovato
        if (token) {
            // Token presente, procediamo con l'autenticazione
        }

    if (!token) {
      // Consente connessioni anche senza token per partite in locale o non autenticate
      // console.log('[SOCKET AUTH DEBUG] Nessun token fornito, connessione non autenticata');
      socket.user = null;
      return next();
    }

    // Usa la stessa chiave JWT del resto dell'applicazione
    const JWT_SECRET = process.env.JWT_SECRET || 'skemino_secret_key';
    
            // Tentativo di verifica del token
    
    // Lista di possibili chiavi JWT per supportare token esistenti
    const possibleSecrets = [
      JWT_SECRET,                 // Chiave principale
      'skemino_secret_key',      // Fallback
      'skemino-jwt-secret-key',  // Vecchia chiave da authController.js
      'skemino-secret-key'       // Vecchia chiave da multiplayer.js
    ];

    let decoded = null;
    let usedSecret = null;

    // Prova a verificare il token con ciascuna delle chiavi possibili
    for (const secret of possibleSecrets) {
      try {
        decoded = jwt.verify(token, secret);
        usedSecret = secret;
                    // Token verificato con successo
            break;
        } catch (verifyError) {
            // Tentativo fallito, prova con la prossima chiave
      }
    }

    if (!decoded) {
      console.error('[SOCKET AUTH DEBUG] Impossibile verificare il token con nessuna delle chiavi disponibili');
      socket.user = null;
      return next();
    }

    // Assicurati che socket.user abbia la struttura corretta
    // console.log('[SOCKET AUTH DEBUG] Token decodificato completo:', JSON.stringify(decoded));

    // Crea una struttura utente compatibile con il resto del codice
    socket.user = {
        id: decoded.id || (decoded.user && decoded.user.id) || 'unknown',
        username: decoded.username || (decoded.user && decoded.user.username) || 'unknown'
    };

            console.log(`User authenticated: ${socket.user.username} (${socket.user.id})`);
    next();
  } catch (err) {
    console.error('[SOCKET AUTH DEBUG] Errore di autenticazione WebSocket:', err.message);
    socket.user = null;
    next();
  }
});

// --- Logica Socket.IO ---
io.on('connection', (socket) => {
    // console.log('Un nuovo giocatore si è connesso:', socket.id);
    
    // Inizializziamo isFullyConnected a false per indicare che il giocatore
    // non è ancora completamente connesso a una partita
    socket.isFullyConnected = false;

    // Aggiunge i metadati dell'utente al socket
    if (socket.user) {
      // console.log(`Utente autenticato: ${socket.user.username} (${socket.user.id})`);
      socket.join(`user:${socket.user.id}`); // Crea una stanza privata per l'utente
    }

    // Gestione log dal client
    socket.on('clientLog', (data) => {
        // console.log(`[CLIENT LOG - ${socket.id}] ${data.message}`);
    });

    // --- GESTIONE MULTIPLAYER ONLINE ---

    /**
     * Trova una partita per matchmaking
     * @event findMatch
     */
    socket.on('findMatch', async (data) => {
        if (!socket.user || !socket.user.id || !socket.user.username) {
            console.log('Matchmaking request denied: user not authenticated');
            socket.emit('matchmakingError', 'Utente non autenticato. Effettua il login per giocare online.');
            return;
        }
        if (!socket.user.id || socket.user.id === 'unknown' || !socket.user.username || socket.user.username === 'unknown') {
            // console.log('[FIND MATCH DEBUG] Dati utente incompleti, richiesta rifiutata');
            // console.log('[FIND MATCH DEBUG] Dati utente:', socket.user);
            socket.emit('matchmakingError', 'Dati utente incompleti. Riprova il login.');
            return;
        }

        // console.log('[FIND MATCH DEBUG] Utente autenticato:', socket.user);

        try {
            const userId = parseInt(socket.user.id, 10); // Assicura che l'ID sia un numero
            if (isNaN(userId)) {
                // console.log('[FIND MATCH DEBUG] ID utente non valido:', socket.user.id);
                socket.emit('matchmakingError', 'ID utente non valido.');
                return;
            }

            const user = await User.findById(userId);
            if (!user) {
                socket.emit('matchmakingError', 'Utente non trovato nel database.');
                return;
            }

            const playerInfo = {
                userId: userId, // Cambiato da 'id' a 'userId'
                socketId: socket.id,
                username: user.username,
                rating: user.rating || 1000 // Usa il rating dell'utente o un default
            };
            await matchmakingController.addToQueue(playerInfo);
            socket.emit('matchmakingStatus', { status: 'inQueue', message: 'Sei in coda per una partita...' });
            console.log(`Player ${user.username} added to matchmaking queue`);

        } catch (error) {
            console.error('Error in findMatch:', error);
            socket.emit('matchmakingError', 'Errore durante la ricerca della partita: ' + error.message);
        }
    });

    /**
     * Cancella la ricerca di una partita
     * @event cancelMatchmaking
     */
    socket.on('cancelMatchmaking', async () => {
        try {
            await matchmakingController.removeFromQueue(socket.id);
            console.log(`[CANCEL MATCHMAKING] Utente ${socket.id} rimosso dalla coda.`);
            socket.emit('matchmakingStatus', { status: 'cancelled', message: 'Ricerca partita annullata.' });
        } catch (error) {
            console.error('Error in cancelMatchmaking:', error);
            socket.emit('matchmakingError', `Errore durante l'annullamento della ricerca: ${error.message}`);
        }
    });

    /**
     * Crea una partita privata
     * @event createPrivateGame
     */
    socket.on('createPrivateGame', async (data) => {
        if (!socket.user) {
            socket.emit('gameError', 'Devi essere autenticato per creare partite private.');
            return;
        }

        // Crea una partita privata
        const gameInfo = gameController.createGame({
            isPrivate: true,
            maxPlayers: data.maxPlayers || 2,
            turnTimeout: data.turnTimeout || 60
        });

        // Aggiungi il creatore alla partita
        gameController.addPlayerToGame(
            gameInfo.gameId,
            socket.id,
            socket.user.username,
            data.rating || 1000
        );

        // Unisci il socket alla stanza della partita
        socket.join(`game:${gameInfo.gameId}`);
        socket.gameId = gameInfo.gameId;

        // Notifica il giocatore
        socket.emit('gameCreated', {
            gameId: gameInfo.gameId,
            inviteCode: gameInfo.inviteCode
        });

        // Salva nel database
        try {
            await GameSession.createSession({
                gameId: gameInfo.gameId,
                maxPlayers: data.maxPlayers || 2,
                turnTimeout: data.turnTimeout || 60,
                isRanked: false // Le partite private non sono ranked
            });

            await GameSession.addPlayer(gameInfo.gameId, {
                userId: socket.user.id,
                playerName: socket.user.username,
                color: 'white', // Il colore verrà assegnato correttamente all'inizio della partita
                rating: data.rating || 1000
            });
        } catch (error) {
            console.error('Errore nel salvataggio della partita:', error);
        }
    });

    /**
     * Unisci a una partita privata tramite codice di invito
     * @event joinPrivateGame
     */
    socket.on('joinPrivateGame', async (data) => {
        if (!socket.user) {
            socket.emit('gameError', 'Devi essere autenticato per unirti a partite private.');
            return;
        }

        const { inviteCode } = data;
        const gameInfo = gameController.findGameByInviteCode(inviteCode);

        if (!gameInfo) {
            socket.emit('gameError', 'Codice di invito non valido o partita non trovata.');
            return;
        }

        // Verifica se la partita è piena
        const gameInstance = gameController.getGame(gameInfo.gameId);
        if (Object.keys(gameInstance.players).length >= gameInstance.maxPlayers) {
            socket.emit('gameError', 'La partita è piena.');
            return;
        }

        // Aggiungi il giocatore alla partita
        const success = gameController.addPlayerToGame(
            gameInfo.gameId,
            socket.id,
            socket.user.username,
            data.rating || 1000
        );

        if (!success) {
            socket.emit('gameError', 'Impossibile unirsi alla partita.');
            return;
        }

        // Unisci il socket alla stanza della partita
        socket.join(`game:${gameInfo.gameId}`);
        socket.gameId = gameInfo.gameId;
        
        console.log(`[ROOM JOIN] Socket ${socket.id} (user: ${socket.user.username}) è entrato nella room: game:${gameInfo.gameId}`);
        console.log(`[ROOM DEBUG] Socket ${socket.id} ora nelle room: ${Array.from(socket.rooms).join(', ')}`);

        // Notifica tutti i giocatori nella partita
        io.to(`game:${gameInfo.gameId}`).emit('playerJoined', {
            playerId: socket.id,
            playerName: socket.user.username
        });

        // Avvia la partita se ci sono abbastanza giocatori
        if (gameInstance && Object.keys(gameInstance.players).length >= 2) {
            const startResult = gameController.startGame(gameInfo.gameId);
            if (startResult.success) {
                // Invia lo stato di gioco a tutti i giocatori
                io.to(`game:${gameInfo.gameId}`).emit('gameStarted');

                // Invia lo stato di gioco a ciascun giocatore
                gameInstance.playerOrder.forEach(playerId => {
                    const playerSocket = io.sockets.sockets.get(playerId);
                    if (playerSocket) {
                        const playerState = gameController.getGameState(gameInfo.gameId, playerId);

                        // Log dettagliato per debug delle mani
                        // console.log(`[SERVER DEBUG] Invio stato a ${playerId} (${gameInstance.playerColors[playerId]})`);
                        if (playerState.players) {
                            Object.keys(playerState.players).forEach(pid => {
                                const player = playerState.players[pid];
                                // console.log(`[SERVER DEBUG] Giocatore ${pid} (${player.color}):`);

                                if (player.hand) {
                                    // console.log(`[SERVER DEBUG]   - Mano: ${player.hand.length} carte`);
                                    // console.log(`[SERVER DEBUG]   - Dettaglio carte:`, JSON.stringify(player.hand.map(c => `${c.value}${c.suit}`)));
                                } else {
                                    // console.log(`[SERVER DEBUG]   - Mano: NON PRESENTE`);
                                }
                            });
                        }

                        playerSocket.emit('gameState', playerState);
                    }
                });
            } else {
                socket.emit('gameError', `Errore avvio partita: ${startResult.reason}`);
            }
        } else {
            // Invia lo stato di gioco al nuovo giocatore
            const gameState = gameController.getGameState(gameInfo.gameId, socket.id);
            if (gameState) {
                socket.emit('gameState', gameState);
            }
        }

        // Salva nel database
        try {
            await GameSession.addPlayer(gameInfo.gameId, {
                userId: socket.user.id,
                playerName: socket.user.username,
                color: 'black', // Il colore verrà assegnato correttamente all'inizio della partita
                rating: data.rating || 1000
            });
        } catch (error) {
            console.error('Errore nel salvataggio del giocatore:', error);
        }
    });

    /**
     * Esegue un'azione di gioco
     * @event gameAction
     */
    socket.on('gameAction', async (data) => {
        const { action, gameId, actionData, playerId } = data;

        if (!gameId) {
            socket.emit('gameError', 'ID partita non valido.');
            return;
        }

        // Azione di gioco ricevuta

        try {
            // Per il multiplayer online, dobbiamo gestire il mapping tra socket ID e player ID
            let effectivePlayerId = socket.id;
            
            // Se abbiamo un playerId esplicito e l'utente è autenticato, usiamo quello
            if (playerId && socket.user) {
                effectivePlayerId = playerId;
                
                // Aggiorna il mapping se necessario
                const game = gameController.getGame(gameId);
                if (game && game.players[playerId] && game.players[playerId].userId === socket.user.id) {
                    // Il playerId è valido per questo utente
                    console.log(`[GAME ACTION] Usando playerId persistente ${playerId} per user ${socket.user.id}`);
                } else {
                    console.log(`[GAME ACTION] PlayerId ${playerId} non valido per user ${socket.user.id}, cercando nel gioco...`);
                    // Cerca il player ID corretto basandosi sull'userId
                    for (const pid in game.players) {
                        if (game.players[pid].userId === socket.user.id) {
                            effectivePlayerId = pid;
                            console.log(`[GAME ACTION] Trovato player ID corretto: ${pid} per user ${socket.user.id}`);
                            break;
                        }
                    }
                }
            }
            
            const result = gameController.handlePlayerAction(gameId, effectivePlayerId, action, actionData);

            if (!result.success) {
                socket.emit('gameError', result.reason || 'Azione di gioco non valida.');
                return;
            }
            
            // Se l'azione ha portato a un controllo di vertice, informa TUTTI i client nella room per la notazione PSN
            if (result.gainedVertexControl) {
                console.log(`[SERVER] Controllo vertice ottenuto per posizione ${result.vertexPosition} (isWhite: ${result.isWhite})`);
                // Invia l'evento a TUTTI i client nella room per sincronizzare la notazione PSN
                io.to(`game:${gameId}`).emit('psnVertexControl', {
                    position: result.vertexPosition, 
                    isWhite: result.isWhite
                });
            }

            // Aggiorna lo stato per tutti i giocatori
            const game = gameController.getGameState(gameId, null);
            if (game) {
                // Invia lo stato di gioco a tutti i giocatori nella room
                // Usa la room invece di cercare socket per playerId
                const room = io.sockets.adapter.rooms.get(`game:${gameId}`);
                // Sincronizzazione stato gioco
                if (room) {
                    // Invia lo stato completo a tutti i client nella room
                    // Il client filtrerà le informazioni in base al proprio playerId
                    room.forEach(socketId => {
                        const playerSocket = io.sockets.sockets.get(socketId);
                        if (playerSocket) {
                            // Trova il playerId corrispondente a questo socket
                            let playerIdForState = null;
                            
                            // Se il socket ha un utente autenticato, cerca il suo playerId nel gioco
                            if (playerSocket.user) {
                                for (const pid in game.players) {
                                    if (game.players[pid].userId === playerSocket.user.id) {
                                        playerIdForState = pid;
                                        break;
                                    }
                                }
                            }
                            
                            // Se non troviamo un match per userId, usa il socketId
                            if (!playerIdForState) {
                                playerIdForState = socketId;
                            }
                            
                            const playerState = gameController.getGameState(gameId, playerIdForState);
                            playerSocket.emit('gameState', playerState);
                            // Stato inviato al client
                        }
                    });
                } else {
                    console.error(`[GAME ACTION] Room game:${gameId} non trovata!`);
                }

                // Registra la mossa nel database
                try {
                    if (socket.user) {
                        await GameMove.createMove({
                            gameId,
                            userId: socket.user.id,
                            type: action,
                            data: actionData
                        });
                    }
                } catch (error) {
                    console.error('Errore nel salvataggio della mossa:', error);
                }

                // Se la partita è finita, aggiorna il database e i rating
                if (game.gameOver) {
                    try {
                        // Salva lo stato finale della partita
                        await GameSession.saveGameState(gameId, game);

                        // Trova il vincitore e il perdente
                        const winnerId = game.winner;
                        const loserId = Object.keys(game.players).find(id => id !== winnerId);

                        // Aggiorna i rating
                        if (winnerId && loserId && socket.user) {
                            // Ottieni i socket dei giocatori
                            const winnerSocket = io.sockets.sockets.get(winnerId);
                            const loserSocket = io.sockets.sockets.get(loserId);

                            if (winnerSocket && winnerSocket.user && loserSocket && loserSocket.user) {
                                // Calcola e aggiorna i rating
                                const ratingResult = await ratingController.updateRatings(gameId, {
                                    winnerId: winnerSocket.user.id,
                                    loserId: loserSocket.user.id
                                });

                                // Termina la sessione di gioco
                                await GameSession.endSession(gameId, {
                                    winnerId: winnerSocket.user.id,
                                    gameState: game
                                });

                                // Invia notifiche di fine partita
                                if (ratingResult) {
                                    // Notifica il vincitore
                                    notificationService.sendGameResultNotification(
                                        winnerSocket.user.id,
                                        gameId,
                                        true,
                                        loserSocket.user.username,
                                        ratingResult.winner.change
                                    );

                                    // Notifica il perdente
                                    notificationService.sendGameResultNotification(
                                        loserSocket.user.id,
                                        gameId,
                                        false,
                                        winnerSocket.user.username,
                                        ratingResult.loser.change
                                    );

                                    // Invia i risultati del rating ai client
                                    io.to(`game:${gameId}`).emit('gameOver', {
                                        gameId,
                                        winnerId: winnerSocket.user.id,
                                        ratingUpdates: ratingResult
                                    });
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Errore nel salvataggio del risultato:', error);
                    }
                } else {
                    // Se non è finita, controlla se è il turno dell'altro giocatore e invia notifica
                    const currentPlayerId = game.currentPlayerId;
                    if (currentPlayerId && currentPlayerId !== socket.id) {
                        const currentPlayerSocket = io.sockets.sockets.get(currentPlayerId);
                        if (currentPlayerSocket && currentPlayerSocket.user && socket.user) {
                            // Invia notifica di turno
                            notificationService.sendTurnNotification(
                                currentPlayerSocket.user.id,
                                gameId,
                                socket.user.username
                            );
                        }
                    }
                }
            }
        } catch (error) {
            console.error('Error in gameAction:', error);
            socket.emit('gameError', 'Errore durante l\'esecuzione dell\'azione di gioco.');
        }
    });

    /**
     * Richiede lo stato attuale del gioco
     * @event requestGameState
     */
    socket.on('requestGameState', async (data) => {
        const reason = data?.reason || 'standard';
        const priority = data?.priority || 'normal';
        const preventTurnTimeout = data?.preventTurnTimeout || false; // NUOVO: Flag per prevenire timeout
        console.log(`[REQUEST GAME STATE] Socket ${socket.id} richiede lo stato del gioco (reason: ${reason}, priority: ${priority}, preventTurnTimeout: ${preventTurnTimeout})`);
        console.log(`[REQUEST GAME STATE] Data ricevuti:`, data);
        console.log(`[REQUEST GAME STATE] Socket user:`, socket.user);
        
        // ✅ GESTIONE PROTEZIONE TURNO: Se è specificato preventTurnTimeout, temporaneamente pausa il timeout
        if (preventTurnTimeout && data?.gameId) {
            console.log(`[REQUEST GAME STATE] Flag preventTurnTimeout attivo - protezione temporanea del turno per game ${data.gameId}`);
            // Usa il gameController per gestire la protezione temporanea del timeout
            if (typeof gameController.temporarilyProtectTurn === 'function') {
                gameController.temporarilyProtectTurn(data.gameId, 5000); // Protezione di 5 secondi
            } else {
                console.log(`[REQUEST GAME STATE] Metodo temporarilyProtectTurn non disponibile nel gameController`);
            }
        }
        
        const requestedGameId = data ? data.gameId : null;
        const userId = socket.user ? socket.user.id : null;
        let mappedGameId = playerGameMap[socket.id];

        console.log(`[REQUEST GAME STATE] mappedGameId da playerGameMap[${socket.id}]: ${mappedGameId}`);
        console.log(`[REQUEST GAME STATE] requestedGameId: ${requestedGameId}`);
        console.log(`[REQUEST GAME STATE] userId: ${userId}`);
        
        // Prima di tutto, verifica se l'utente ha una partita attiva usando userGameMap
        let gameIdFromUserMap = userGameMap[userId];
        if (gameIdFromUserMap) {
            console.log(`[REQUEST GAME STATE] Trovata partita in userGameMap: userId ${userId} -> gameId ${gameIdFromUserMap}`);
            mappedGameId = gameIdFromUserMap;
            playerGameMap[socket.id] = gameIdFromUserMap; // Aggiorna anche playerGameMap
            
            // Se la partita è in userGameMap ma non in activeGames, potrebbe essere un problema di sync
            if (!activeGames[gameIdFromUserMap]) {
                console.log(`[REQUEST GAME STATE] ATTENZIONE: Partita ${gameIdFromUserMap} in userGameMap ma non in activeGames!`);
                console.log(`[REQUEST GAME STATE] activeGames keys: ${Object.keys(activeGames).join(', ')}`);
                
                // Prova a recuperare la partita dal controller
                const controllerGame = gameController.getGame(gameIdFromUserMap);
                if (controllerGame) {
                    activeGames[gameIdFromUserMap] = controllerGame;
                    console.log(`[REQUEST GAME STATE] Partita ${gameIdFromUserMap} recuperata dal controller e aggiunta ad activeGames`);
                } else {
                    console.log(`[REQUEST GAME STATE] Partita ${gameIdFromUserMap} non trovata neanche nel controller`);
                }
            }
        }

        // Se non troviamo il gameId tramite socket o userGameMap, proviamo a cercarlo per userId
        if (!mappedGameId && requestedGameId && userId) {
            console.log(`[REQUEST GAME STATE] Cerco partita ${requestedGameId} per userId ${userId}`);
            
            // Verifica se l'utente fa parte di questa partita
            let game = activeGames[requestedGameId];
            
            // Se non è in activeGames, controlla nel gameController (per partite online)
            if (!game) {
                const controllerGame = gameController.getGame(requestedGameId);
                if (controllerGame) {
                    console.log(`[REQUEST GAME STATE] Partita ${requestedGameId} trovata nel gameController ma non in activeGames`);
                    game = controllerGame;
                    // Sincronizza con activeGames per future richieste
                    activeGames[requestedGameId] = controllerGame;
                }
            }
            
            if (game) {
                console.log(`[REQUEST GAME STATE] Partita trovata, giocatori:`, Object.keys(game.players));
                console.log(`[REQUEST GAME STATE] Dettaglio giocatori:`, JSON.stringify(game.players, null, 2));
                
                // Cerca tra i giocatori della partita
                const playerInGame = Object.values(game.players).find(p => {
                    console.log(`[REQUEST GAME STATE] Confronto: p.userId=${p.userId}, p.id=${p.id}, p.username=${p.username} con userId=${userId}, username=${socket.user.username}`);
                    return p.userId === userId || p.id === userId || p.username === socket.user.username;
                });
                
                if (playerInGame) {
                    console.log(`[REQUEST GAME STATE] Giocatore trovato nella partita:`, playerInGame);
                    
                    // Aggiorna la mappatura con il nuovo socket ID
                    mappedGameId = requestedGameId;
                    playerGameMap[socket.id] = requestedGameId;
                    
                    // Aggiungi il socket alla room del gioco
                    socket.join(`game:${requestedGameId}`);
                    socket.gameId = requestedGameId;
                    
                    // Aggiorna anche nel game
                    const oldSocketId = Object.keys(game.players).find(sid => {
                        const player = game.players[sid];
                        return player.userId === userId || player.id === userId || player.username === socket.user.username;
                    });
                    
                    console.log(`[REQUEST GAME STATE] oldSocketId trovato: ${oldSocketId}`);
                    
                    if (oldSocketId && oldSocketId !== socket.id) {
                        // Trasferisci i dati del giocatore al nuovo socket
                        const playerData = game.players[oldSocketId];
                        delete game.players[oldSocketId];
                        game.players[socket.id] = { ...playerData };
                        game.players[socket.id].socketId = socket.id;
                        
                        // Aggiorna anche playerOrder con il nuovo socket ID
                        const orderIndex = game.playerOrder.indexOf(oldSocketId);
                        if (orderIndex !== -1) {
                            game.playerOrder[orderIndex] = socket.id;
                        }
                        
                        // Aggiorna playerColors
                        const color = game.playerColors[oldSocketId];
                        if (color) {
                            delete game.playerColors[oldSocketId];
                            game.playerColors[socket.id] = color;
                        }
                        
                        // Aggiorna playerNames se presente
                        const name = game.playerNames[oldSocketId];
                        if (name) {
                            delete game.playerNames[oldSocketId];
                            game.playerNames[socket.id] = name;
                        }
                        
                        // Aggiorna playerRatings se presente
                        const rating = game.playerRatings[oldSocketId];
                        if (rating) {
                            delete game.playerRatings[oldSocketId];
                            game.playerRatings[socket.id] = rating;
                        }
                        
                        // Rimuovi vecchia mappatura
                        delete playerGameMap[oldSocketId];
                        
                        console.log(`[SOCKET UPDATE] Aggiornato socket per user ${userId}: ${oldSocketId} -> ${socket.id}`);
                    }
                } else {
                    console.log(`[REQUEST GAME STATE] Giocatore NON trovato nella partita`);
                }
            } else {
                console.log(`[REQUEST GAME STATE] Partita ${requestedGameId} NON trovata in activeGames`);
            }
        }

        console.log(`[REQUEST GAME STATE DEBUG] socket.gameId = ${requestedGameId}, playerGameMap[${socket.id}] = ${mappedGameId}`);

        if (!userId) {
            socket.emit('gameError', 'Utente non autenticato.');
            return;
        }

        // Tentativo di riconnessione se l'utente era disconnesso
        if (!mappedGameId && userId) {
            // console.log(`[RECONNECTION CHECK] Verifica se l'utente ${userId} era disconnesso`);
            try {
                const reconnectResult = gameController.handlePlayerReconnection(userId, socket.id);
                if (reconnectResult && reconnectResult.event === 'reconnectionSuccessful') {
                    mappedGameId = reconnectResult.gameId;
                    playerGameMap[socket.id] = mappedGameId; // Aggiorna la mappa locale del server
                    
                    // UNISCI IL SOCKET ALLA ROOM DELLA PARTITA DOPO RICONNESSIONE
                    socket.join(`game:${mappedGameId}`);
                    console.log(`[RECONNECTION] Socket ${socket.id} joined room game:${mappedGameId} after reconnection`);
                    
                    // Invia lo stato di gioco riconnesso al client
                    socket.emit('reconnectionSuccessful', reconnectResult);
                    // console.log(`[RECONNECTION SUCCESS] L'utente ${userId} si è riconnesso alla partita ${reconnectResult.gameId}`);

                } else {
                    // console.log(`[RECONNECTION FAILED] Nessuna partita in corso trovata per l'utente ${userId} o errore: ${reconnectResult.message}`);
                    // Non inviare gameError qui, potrebbe essere un nuovo utente senza partite attive
                }
            } catch (error) {
                console.error('Errore nel registrare la riconnessione del giocatore:', error);
                // Non inviare gameError qui per non interrompere un nuovo utente
            }
        }


        // Se dopo il tentativo di riconnessione c'è ancora un gameId (da richiesta o da mappa)
        const finalGameId = requestedGameId || mappedGameId;

        if (finalGameId) {
            let game = activeGames[finalGameId];
            
            // Se non è in activeGames, controlla nel gameController (per partite online)
            if (!game) {
                const controllerGame = gameController.getGame(finalGameId);
                if (controllerGame) {
                    console.log(`[REQUEST GAME STATE] Partita ${finalGameId} trovata nel gameController ma non in activeGames`);
                    game = controllerGame;
                    // Sincronizza con activeGames per future richieste
                    activeGames[finalGameId] = controllerGame;
                }
            }
            
            if (game) {
                console.log(`[REQUEST GAME STATE] Trovata partita attiva ${finalGameId}`);
                console.log(`[REQUEST GAME STATE] Giocatori nella partita:`, Object.keys(game.players));

                // Assicurati che il giocatore che richiede lo stato sia parte di quella partita
                const playerIsInGame = Object.values(game.players).some(p => {
                    console.log(`[REQUEST GAME STATE] Controllo giocatore: p.id=${p.id}, p.userId=${p.userId}, userId=${userId}`);
                    return String(p.id) === String(userId) || String(p.userId) === String(userId);
                });

                if (playerIsInGame) {
                    console.log(`[REQUEST GAME STATE] Giocatore ${userId} trovato nella partita`);

                    // UNISCI IL SOCKET ALLA ROOM DELLA PARTITA
                    socket.join(`game:${finalGameId}`);
                    console.log(`[REQUEST GAME STATE] Socket ${socket.id} joined room game:${finalGameId} via requestGameState`);

                    // Trova l'ID del giocatore corretto basandosi sull'userId
                    let correctPlayerId = null;
                    for (const [playerId, player] of Object.entries(game.players)) {
                        if (String(player.userId) === String(userId)) {
                            correctPlayerId = playerId;
                            break;
                        }
                    }

                    console.log(`[REQUEST GAME STATE] Trovato playerId corretto: ${correctPlayerId} per userId: ${userId}`);

                    const gameState = game.getState(correctPlayerId);
                    
                    // ✅ PULIZIA PREVENTIVA SEMPRE: Per tutte le richieste di stato
                    if (game && game.cleanupDuplicateCards) {
                        console.log(`[REQUEST GAME STATE] Eseguendo pulizia carte duplicate preventiva per ${reason || 'standard'}`);
                        game.cleanupDuplicateCards();
                        // Salva lo stato pulito
                        if (typeof gameController.saveGameState === 'function') {
                            gameController.saveGameState(finalGameId);
                        }
                        
                        // Riottieni lo stato dopo la pulizia
                        const cleanGameState = game.getState(correctPlayerId);
                        console.log(`[REQUEST GAME STATE] Stato pulito riottento per ${correctPlayerId}`);
                        
                        console.log(`[REQUEST GAME STATE] Invio stato PULITO ${finalGameId} a ${socket.id} (reason: ${reason})`);
                        socket.emit('gameState', cleanGameState);
                        return; // Esci qui per evitare doppio invio
                    }
                    
                    // ✅ VALIDAZIONE FINALE se la pulizia automatica non è disponibile
                    if (reason.includes('setup_interrupted') || priority === 'high') {
                        console.log(`[REQUEST GAME STATE] Richiesta prioritaria (${reason}) - validazione manuale mani`);
                        
                        // Per richieste di setup interrotto, verifica manualmente che le mani siano pulite
                        if (gameState && gameState.players) {
                            // Verifica che non ci siano carte duplicate tra board e mani
                            const boardCardIds = new Set();
                            if (gameState.board) {
                                Object.values(gameState.board).forEach(card => {
                                    if (card && card.id) {
                                        boardCardIds.add(card.id);
                                    }
                                });
                            }
                            
                            let totalFixed = 0;
                            // Pulisci le mani da eventuali duplicati
                            Object.keys(gameState.players).forEach(playerId => {
                                const player = gameState.players[playerId];
                                if (player && player.hand) {
                                    const originalHandSize = player.hand.length;
                                    player.hand = player.hand.filter(card => {
                                        if (card && card.id && boardCardIds.has(card.id)) {
                                            console.log(`[REQUEST GAME STATE] ⚠️  CORREZIONE FINALE: Rimossa carta duplicata ${card.id} dalla mano di ${playerId}`);
                                            totalFixed++;
                                            return false;
                                        }
                                        return true;
                                    });
                                    
                                    if (player.hand.length !== originalHandSize) {
                                        console.log(`[REQUEST GAME STATE] Pulita mano di ${playerId}: ${originalHandSize} → ${player.hand.length} carte`);
                                        // Aggiorna handSize se presente
                                        if (player.handSize !== undefined) {
                                            player.handSize = player.hand.length;
                                        }
                                    }
                                }
                            });
                            
                            if (totalFixed > 0) {
                                console.log(`[REQUEST GAME STATE] ⚠️  CORREZIONE FINALE: Rimosse ${totalFixed} carte duplicate per richiesta urgente`);
                            } else {
                                console.log(`[REQUEST GAME STATE] ✅ Validazione manuale OK - nessuna carta duplicata`);
                            }
                        }
                    }
                    
                    console.log(`[REQUEST GAME STATE] Invio stato partita ${finalGameId} a ${socket.id} (reason: ${reason})`);
                    socket.emit('gameState', gameState);
                } else {
                    console.log(`[REQUEST GAME STATE] Utente ${userId} (${socket.id}) non fa parte della partita ${finalGameId}`);
                    socket.emit('gameError', 'Non fai parte di questa partita.');
                }
            } else if (finalGameId) {
                console.log(`[REQUEST GAME STATE] Partita ${finalGameId} non trovata in activeGames. Tentativo di recupero da DB.`);
                // Prova a recuperare lo stato della partita dal database se non è attiva (potrebbe essere finita)
                try {
                const gameSession = await GameSession.getSessionById(finalGameId);
                if (gameSession) {
                    // console.log(`[REQUEST GAME STATE] Partita ${finalGameId} trovata nel DB.`);
                    // Recupera i dati dei giocatori
                    const players = await GameSession.getGamePlayers(finalGameId);
                    
                    if (players) {
                        const player1 = players.player1;
                        const player2 = players.player2;
                        
                        const isPlayer1 = player1 && String(player1.id) === String(userId);
                        const isPlayer2 = player2 && String(player2.id) === String(userId);
                        
                        if (isPlayer1 || isPlayer2) {
                            const opponent = isPlayer1 ? player2 : player1;
                            const player = isPlayer1 ? player1 : player2;
                            
                            socket.emit('gameState', {
                                gameId: finalGameId,
                                isGameOver: true,
                                winner: gameSession.winnerId,
                                status: gameSession.status,
                                playerScore: isPlayer1 ? player1.finalScore : player2.finalScore,
                                opponentScore: isPlayer1 ? player2.finalScore : player1.finalScore,
                                opponentName: opponent ? opponent.name : 'Sconosciuto',
                                playerColor: isPlayer1 ? player1.color : player2.color,
                                message: 'Questa partita è terminata.'
                            });
                        } else {
                            socket.emit('gameError', 'Non fai parte di questa partita.');
                        }
                    } else {
                        socket.emit('gameError', 'Impossibile recuperare i dati dei giocatori.');
                    }
                } else {
                    console.log(`[REQUEST GAME STATE] Partita ${finalGameId} non trovata né attiva né nel DB.`);
                    console.log(`[REQUEST GAME STATE] activeGames keys:`, Object.keys(activeGames));
                    socket.emit('gameError', 'Partita non trovata.');
                }
                } catch (dbError) {
                    console.error('Errore nel recupero della partita dal database:', dbError);
                    socket.emit('gameError', 'Errore nel recuperare lo stato della partita.');
                }
            }
        } else {
            // console.log(`[REQUEST GAME STATE] Nessun gameId fornito e nessuna partita mappata per ${socket.id}.`);
            // Non inviare un errore qui, potrebbe essere un client che si connette per la prima volta
            // e non ha ancora una partita. Invia uno stato "nessuna partita attiva".
            socket.emit('noActiveGame');
        }
    });

    // --- GESTIONE GIOCO LOCALE (CODICE ESISTENTE) ---

    // Gestione disconnessione
    socket.on('disconnect', async () => {
        console.log('Un giocatore si è disconnesso:', socket.id);

        // Se l'utente era autenticato, mantieni il riferimento alla sua partita
        if (socket.user && socket.user.id) {
            const gameId = userGameMap[socket.user.id];
            if (gameId) {
                console.log(`[DISCONNECT] L'utente ${socket.user.id} (${socket.user.username}) rimane associato alla partita ${gameId}`);
            }
        }

        // Rimuovi il giocatore dalla mappa socket e dalla coda di matchmaking
        const gameId = playerGameMap[socket.id];
        let game = activeGames[gameId];
        
        // Se non è in activeGames, controlla nel gameController (per partite online)
        if (!game && gameId) {
            const controllerGame = gameController.getGame(gameId);
            if (controllerGame) {
                console.log(`[DISCONNECT] Partita ${gameId} trovata nel gameController ma non in activeGames`);
                game = controllerGame;
            }
        }
        
        if (gameId && game) {
            let remainingPlayerId = null;

            // Potrebbe essere necessario gestire la disconnessione in modo diverso
            // a seconda che il giocatore fosse in una partita o meno.
            // console.log(`[DISCONNECT] Giocatore ${socket.id} rimosso da playerGameMap.`);

            // Rimuovi dalla coda di matchmaking se presente
            console.log(`[DISCONNECT] Tentativo di rimuovere ${socket.id} dalla matchmaking queue`);
            await matchmakingController.removeFromQueue(socket.id);
            console.log(`[DISCONNECT] Rimosso ${socket.id} dalla matchmaking queue (se era presente)`);

            if (game.gameOver && remainingPlayerId) {
                // Se la partita è finita a causa della disconnessione, informa il vincitore
                const winnerSocket = io.sockets.sockets.get(game.winner);
                if (winnerSocket) {
                    winnerSocket.emit('gameState', game.getState(game.winner));
                }
            } else if (remainingPlayerId && game.players[remainingPlayerId] && !game.players[remainingPlayerId].isAI) {
                // Notifica al giocatore rimanente che l'avversario si è disconnesso
                io.to(remainingPlayerId).emit('opponentDisconnected', { opponentName: game.playerNames[remainingPlayerId] });
                // console.log(`[DISCONNECT] Notificato a ${remainingPlayerId} la disconnessione di ${remainingPlayerId}`);

                // Gestione della vittoria per abbandono
                if (game.gameState !== 'completed') {
                    game.gameState = 'completed';
                    game.winner = remainingPlayerId;

                    // console.log(`[DISCONNECT] Partita ${gameId} terminata. Vincitore: ${remainingPlayerId} per abbandono di ${remainingPlayerId}.`);

                    // Salva il risultato della partita
                    const gameSession = await GameSession.findByPk(gameId);
                    if (gameSession) {
                        gameSession.status = 'completed';
                        gameSession.winner_player_id = game.winner;
                        gameSession.end_time = new Date();
                        // gameSession.final_score_player1 = game.players[gameSession.player1_id].score;
                        // gameSession.final_score_player2 = game.players[gameSession.player2_id].score;
                        await gameSession.save();
                        // console.log(`[DISCONNECT] Rating aggiornati per la partita ${gameId}`);
                    }

                    // Emetti stato di gioco finale
                    emitGameStateToPlayers(gameId, game);
                    // console.log(`[DISCONNECT] Emesso stato finale della partita ${gameId} ai giocatori.`);
                }
            } else if (remainingPlayerId && game.players[remainingPlayerId] && game.players[remainingPlayerId].isAI) {
                // L'avversario era l'IA, la partita può considerarsi terminata
                // console.log(`[DISCONNECT] Il giocatore ${remainingPlayerId} si è disconnesso da una partita contro l'IA (${gameId}). Partita terminata.`);
                // Potrebbe essere utile marcare la partita come abbandonata/terminata nel database
                // ...logica per gestire l'abbandono di una partita vs AI...
                delete activeGames[gameId]; // Rimuovi la partita dalla memoria attiva
                // console.log(`[DISCONNECT] Partita contro IA ${gameId} rimossa da activeGames.`);
            } else {
                // Entrambi i giocatori si sono disconnessi o la partita era già terminata
                console.log(`[DISCONNECT] Nessun giocatore rimanente nella partita ${gameId}.`);
                
                // NON rimuovere immediatamente le partite multiplayer online
                // Verifica se è una partita online che potrebbe avere riconnessioni
                if (game.mode === 'online' && !game.gameOver) {
                    console.log(`[DISCONNECT] Partita online ${gameId} mantenuta in memoria per possibili riconnessioni.`);
                    // La partita verrà rimossa dopo il timeout di cleanup
                } else {
                    console.log(`[DISCONNECT] Partita ${gameId} terminata o locale. Rimozione da activeGames.`);
                    delete activeGames[gameId];
                }
            }
        } else {
            // console.log(`[DISCONNECT] Nessuna partita attiva trovata per il socket ${socket.id}.`);
        }

        // Rimuovi da waitingPlayer se era in attesa
        if (waitingPlayer === socket.id) {
            waitingPlayer = null;
            // console.log(`[DISCONNECT] Giocatore ${socket.id} rimosso da waitingPlayer.`);
        }
    });

    // Gestione richiesta di inizio partita
    socket.on('startGame', ({ mode, player1Name, player2Name }) => {
        // console.log(`Giocatore ${socket.id} vuole iniziare una partita in modalità: ${mode}`);
        let game;
        let gameId;

        if (mode === 'ai') {
            gameId = `ai-${uuidv4()}`;
            game = new Game(gameId, 'ai');
            activeGames[gameId] = game;
            game.addPlayer(socket.id); // Aggiunge il giocatore umano
            playerGameMap[socket.id] = gameId;
            // console.log(`Giocatore umano ${socket.id} aggiunto alla partita AI ${gameId}.`);

            // Aggiungi esplicitamente il giocatore AI
            game.addPlayer('ai_player');
            // playerGameMap['ai_player'] = gameId; // Non necessario mappare l'AI senza socket

            // console.log(`Creata partita vs AI con ID: ${gameId} per giocatore ${socket.id}. Avvio partita...`);

            // Ora avvia la partita DOPO aver aggiunto entrambi i giocatori
            const startResult = game.startGame();
            if(startResult.success) {
                // getState ora include già initialCard e initialPosition se disponibili
                const initialState = game.getState(null, true);
                emitGameStateToPlayers(gameId, initialState); // Invia lo stato iniziale
            } else {
                socket.emit('gameError', `Errore avvio partita AI: ${startResult.reason}`);
                delete activeGames[gameId]; // Pulisci se fallisce
                delete playerGameMap[socket.id];
            }

        } else if (mode === 'local') {
            // Modalità Locale: crea subito partita con 2 giocatori logici per questo socket
            gameId = `local-${uuidv4()}`;
            game = new Game(gameId, 'local'); // Usa la nuova modalità
            activeGames[gameId] = game;

            // Crea ID logici per i due giocatori locali
            const player1Id = socket.id + '_p1';
            const player2Id = socket.id + '_p2';
            console.log(`[LOCAL GAME SETUP] Creating local game with player1Id: ${player1Id}, player2Id: ${player2Id}`);
            
            game.localPlayerIds = [player1Id, player2Id]; // Salva gli ID logici
            game.hostSocketId = socket.id; // Salva il socket ID reale

            // Ottieni i nomi personalizzati o usa valori predefiniti
            const p1Name = player1Name || "Giocatore 1";
            const p2Name = player2Name || "Giocatore 2";
            console.log(`[LOCAL GAME SETUP] Player names: ${p1Name} (white), ${p2Name} (black)`);

            // Forza l'ordine giusto pulendo eventuali giocatori preesistenti
            game.players = {};
            game.playerOrder = [];
            game.playerColors = {};
            
            // Aggiungi i giocatori logici alla partita con i nomi personalizzati
            game.addPlayer(player1Id, p1Name); // DOVREBBE essere White
            console.log(`[LOCAL GAME SETUP] After adding p1, color is:`, game.playerColors[player1Id]);
            
            game.addPlayer(player2Id, p2Name); // DOVREBBE essere Black
            console.log(`[LOCAL GAME SETUP] After adding p2, color is:`, game.playerColors[player2Id]);
            
            console.log(`[LOCAL GAME SETUP] Player colors after adding:`, game.playerColors);
            console.log(`[LOCAL GAME SETUP] Player order:`, game.playerOrder);

            // Mappa il socket reale alla partita
            playerGameMap[socket.id] = gameId;

            // console.log(`Partita Locale ${gameId} creata per socket ${socket.id} (Giocatori: ${p1Name}, ${p2Name})`);

            // Avvia la partita
            const startResult = game.startGame();
            if(startResult.success) {
                // getState ora include già initialCard e initialPosition se disponibili
                const initialState = game.getState(null, true);
                
                // Log dettagliato delle mani dei giocatori
                console.log(`[LOCAL GAME] Stato iniziale per la partita ${gameId}:`);
                Object.keys(initialState.players).forEach(playerId => {
                    const player = initialState.players[playerId];
                    console.log(`[LOCAL GAME] ${playerId} (${player.color}) ha ${player.hand?.length || 0} carte:`, 
                        player.hand?.map(c => `${c.suit}-${c.value}`) || 'nessuna carta');
                });
                
                // Log per verifica (opzionale)
                if (initialState.initialCard) {
                     console.log(`Partita Locale ${gameId}: Stato iniziale con carta ${initialState.initialCard.value} ${initialState.initialCard.suit} su ${initialState.initialPosition} pronto.`);
                } else {
                     console.warn(`Partita Locale ${gameId}: Stato iniziale generato senza initialCard/initialPosition.`);
                }
                // Invia lo stato iniziale al singolo client
                emitGameStateToPlayers(gameId, initialState);
            } else {
                socket.emit('gameError', `Errore avvio partita locale: ${startResult.reason}`);
                delete activeGames[gameId]; // Pulisci se fallisce
                delete playerGameMap[socket.id];
            }
        }
    });

    // Gestione azioni di gioco (passa l'ID logico corretto)
    socket.on('placeCard', ({ playerId, card, position }) => {
        console.log(`[placeCard] Ricevuto: playerId=${playerId}, card=${card.suit}-${card.value}, position=${position}`);
        const gameId = playerGameMap[socket.id];

        if (!gameId) {
            console.error(`[GAME ERROR] Nessuna partita mappata per il socket ${socket.id} durante placeCard`);
            // Non invio l'errore perché potrebbe interferire durante la fase di match found
            // socket.emit('gameError', 'Nessuna partita attiva trovata. Prova a tornare al menu principale e iniziare una nuova partita.');
            return;
        }

        if (!activeGames[gameId]) {
            console.error(`[GAME ERROR] Partita ${gameId} non trovata per il socket ${socket.id} durante placeCard`);
            // Pulisci la mappatura errata
            delete playerGameMap[socket.id];
            // Non invio l'errore perché potrebbe interferire durante la fase di match found
            // socket.emit('gameError', 'La partita non è più attiva. Prova a iniziare una nuova partita.');
            return;
        }

        const game = activeGames[gameId];
        if (game.mode === 'local') {
            // Ottieni il playerId logico corretto dal server
            console.log(`[placeCard] Gioco locale, converto playerId`);
            const actualPlayerId = convertToLogicalPlayerId(socket.id, playerId, game);
            console.log(`[placeCard] ID convertito: ${playerId} -> ${actualPlayerId}`);
            const result = game.handlePlaceCard(actualPlayerId, card, position);

            // Gestione speciale per il caso di ribaltone fallito
            if (!result.success) {
                if (result.ribaltoneFailedWin) {
                    // Se è un tentativo di ribaltone fallito, mostra il messaggio di errore
                    // ma invia comunque lo stato aggiornato per mostrare la vittoria dell'avversario
                    socket.emit('invalidMove', result.reason);

                    // Invia lo stato aggiornato che mostrerà la vittoria dell'avversario
                    socket.emit('gameState', game.getState(null, true));
                } else {
                    // Per altri errori, comportamento standard
                    socket.emit('gameError', result.reason);
                    return;
                }
            } else {
                // Invia lo stato aggiornato
                socket.emit('gameState', game.getState(null, true));
            }

            // Invia gli aggiornamenti di rating se la partita è terminata
            if (game.gameOver) {
                const ratingUpdates = game.updateRatings(game.winner);
                if (ratingUpdates) {
                    // console.log('DEBUG: Invio gameOver con ratingUpdates', ratingUpdates);

                    // Aggiorna i rating persistenti
                    if (ratingUpdates.winner) {
                        const winnerId = game.winner;
                        playerRatings[winnerId] = {
                            rating: ratingUpdates.winner.newRating,
                            playerName: game.playerNames[winnerId]
                        };
                    }

                    if (ratingUpdates.loser) {
                        const loserId = game.playerOrder.find(id => id !== game.winner);
                        playerRatings[loserId] = {
                            rating: ratingUpdates.loser.newRating,
                            playerName: game.playerNames[loserId]
                        };
                    }

                    socket.emit('gameOver', {
                        winnerId: game.winner,
                        ratingUpdates: ratingUpdates
                    });
                }
            }

            // Se è il turno dell'AI, esegui la sua mossa
            handleAiTurn(game, socket);
        } else { // Modalità AI o online
            handleGameAction(socket, (game, logicalPlayerId) => game.handlePlaceCard(logicalPlayerId, card, position), playerId);
        }
    });

    socket.on('placeCardOnVertex', ({ playerId, card, vertexId }) => { // Aspetta playerId
        handleGameAction(socket, (game, logicalPlayerId) => game.handlePlaceCardOnVertex(logicalPlayerId, card, vertexId), playerId);
    });

    // Nuovo endpoint per gestire il piazzamento di una carta durante il turno di ERA4 (Ribaltone)
    socket.on('ribaltoneMove', ({ playerId, card, position }) => {
        // console.log(`Giocatore ${playerId} vuole eseguire un Ribaltone con ${card.suit}-${card.value} in ${position}`);
        handleGameAction(socket, (game, logicalPlayerId) => game.handleRibaltoneMove(logicalPlayerId, card, position), playerId);
    });

    /**
     * Gestisce la richiesta di un giocatore di pescare una carta.
     * I giocatori possono sempre pescare una carta, indipendentemente dal fatto che abbiano mosse valide o meno,
     * fino a un massimo di 10 carte in mano.
     */
    // Gestione eventi di animazione e visibilità per coordinare animazioni tra client
    socket.on('clientVisibilityChange', (data) => {
        console.log(`[VISIBILITY] Client ${socket.id} ha cambiato stato di visibilità: ${data.state}`, 
                   data.setupInterrupted ? '(setup interrotto)' : '', 
                   data.wasSetupInterrupted ? '(era setup interrotto)' : '');
        
        // ✅ NUOVO: Aggiorna lo stato di visibilità nel gameController per gestione timeout
        const isVisible = data.state === 'visible';
        gameController.updatePlayerVisibility(socket.id, isVisible);
        
        if (data.gameId) {
            // ✅ CORREZIONE SETUP INTERROTTO: Gestione speciale per setup interrotto
            if (data.state === 'visible' && data.wasSetupInterrupted) {
                console.log(`[VISIBILITY] Client ${socket.id} tornato visibile dopo setup interrotto - forzando sincronizzazione SICURA`);
                
                // Forza invio dello stato aggiornato con validazione carte duplicate
                const game = activeGames[data.gameId] || gameController.getGame(data.gameId);
                if (game) {
                    // Trova l'ID del giocatore corretto
                    let correctPlayerId = null;
                    if (socket.user && socket.user.id) {
                        for (const [playerId, player] of Object.entries(game.players)) {
                            if (String(player.userId) === String(socket.user.id)) {
                                correctPlayerId = playerId;
                                break;
                            }
                        }
                    }
                    
                    if (correctPlayerId) {
                        // ✅ CORREZIONE CRITICA: Pulisci PRIMA di ottenere qualsiasi stato
                        if (game && game.cleanupDuplicateCards) {
                            console.log(`[VISIBILITY SERVER FIX] Eseguendo pulizia carte duplicate nel gioco PRIMA di inviare stato`);
                            game.cleanupDuplicateCards();
                            
                            // Salva lo stato pulito immediatamente
                            gameController.saveGameState(data.gameId);
                            console.log(`[VISIBILITY SERVER FIX] Stato del gioco salvato dopo pulizia automatica`);
                        }
                        
                        // Ottieni lo stato pulito DOPO la pulizia
                        const cleanGameState = game.getState(correctPlayerId);
                        
                        // ✅ DOPPIA VALIDAZIONE: Controlla anche lo stato inviato al client
                        if (cleanGameState && cleanGameState.board && cleanGameState.players) {
                            const boardCardIds = new Set();
                            
                            // Raccogli tutti gli ID delle carte sul board
                            Object.values(cleanGameState.board).forEach(card => {
                                if (card && card.id) {
                                    boardCardIds.add(card.id);
                                }
                            });
                            
                            // Pulisci le mani da eventuali carte duplicate rimaste
                            let totalFixed = 0;
                            Object.keys(cleanGameState.players).forEach(playerId => {
                                const player = cleanGameState.players[playerId];
                                if (player && player.hand && Array.isArray(player.hand)) {
                                    const originalSize = player.hand.length;
                                    player.hand = player.hand.filter(card => {
                                        if (card && card.id && boardCardIds.has(card.id)) {
                                            console.log(`[VISIBILITY SERVER FIX] Rimossa carta duplicata FINALE ${card.id} dalla mano di ${playerId}`);
                                            totalFixed++;
                                            return false;
                                        }
                                        return true;
                                    });
                                    
                                    // Aggiorna handSize se è cambiato
                                    if (originalSize !== player.hand.length && player.handSize !== undefined) {
                                        player.handSize = player.hand.length;
                                    }
                                }
                            });
                            
                            if (totalFixed > 0) {
                                console.log(`[VISIBILITY SERVER FIX] ⚠️  CORREZIONE FINALE: Rimosse ${totalFixed} carte duplicate dallo stato prima dell'invio`);
                            } else {
                                console.log(`[VISIBILITY SERVER FIX] ✅ Stato finale verificato - nessuna carta duplicata`);
                            }
                        }
                        
                        socket.emit('gameState', cleanGameState);
                        console.log(`[VISIBILITY] Stato VALIDATO e PULITO inviato immediatamente a ${socket.id} dopo setup interrotto`);
                    }
                }
            }
            
            // Notifica l'altro giocatore del cambiamento di visibilità
            socket.to(`game:${data.gameId}`).emit('otherClientVisibilityChange', {
                clientId: socket.id,
                state: data.state,
                isAnimatingInitialCard: data.isAnimatingInitialCard || false,
                setupInterrupted: data.setupInterrupted || false,
                wasSetupInterrupted: data.wasSetupInterrupted || false
            });
            
            console.log(`[VISIBILITY] Notificato cambio di visibilità agli altri client per il gioco ${data.gameId}`);
        }
    });
    


    // Gestione messaggi chat
    socket.on('chatMessage', (data) => {
        console.log(`[CHAT] Ricevuto messaggio da ${socket.id} nella partita ${data.gameId}: ${data.message}`);
        
        if (!socket.user) {
            console.log('[CHAT] Messaggio rifiutato: utente non autenticato');
            socket.emit('gameError', 'Devi essere autenticato per inviare messaggi in chat.');
            return;
        }
        
        if (!data.gameId) {
            console.log('[CHAT] Messaggio rifiutato: gameId non specificato');
            return;
        }
        
        // Verifica che il giocatore sia nella partita
        const game = activeGames[data.gameId];
        if (!game) {
            console.log(`[CHAT] Messaggio rifiutato: partita ${data.gameId} non trovata`);
            return;
        }
        
        // Verifica che il giocatore sia uno dei giocatori della partita
        // Per multiplayer online, dobbiamo verificare anche tramite user ID
        let isPlayerInGame = false;
        let effectivePlayerId = socket.id;
        
        if (game.players) {
            // Controlla prima se il socket ID è direttamente nella partita
            if (game.players[socket.id]) {
                isPlayerInGame = true;
                effectivePlayerId = socket.id;
            }
            // Se non trovato e l'utente è autenticato, cerca tramite user ID
            else if (socket.user && socket.user.id) {
                for (const playerId in game.players) {
                    const player = game.players[playerId];
                    if (player && String(player.userId) === String(socket.user.id)) {
                        isPlayerInGame = true;
                        effectivePlayerId = playerId;
                        console.log(`[CHAT DEBUG] Trovato giocatore tramite user ID: ${socket.user.id} -> ${playerId}`);
                        break;
                    }
                }
            }
        }
        
        if (!isPlayerInGame) {
            console.log(`[CHAT] Messaggio rifiutato: giocatore ${socket.id} (user: ${socket.user?.id}) non è nella partita ${data.gameId}`);
            return;
        }
        
        // Limita la lunghezza del messaggio
        const message = data.message.substring(0, 100);
        
        // Sanitizza il messaggio (rimuovi HTML/script per sicurezza)
        const sanitizedMessage = message.replace(/</g, '&lt;').replace(/>/g, '&gt;');
        
        // Ottieni il nome del giocatore usando l'effectivePlayerId
        const playerName = socket.user?.username || 
                          game.playerNames?.[effectivePlayerId] || 
                          game.players?.[effectivePlayerId]?.username || 
                          'Giocatore';
        
        // Invia il messaggio a tutti i giocatori nella partita, incluso il mittente
        const chatMessageData = {
            senderId: socket.id,
            senderName: playerName,
            message: sanitizedMessage,
            timestamp: Date.now()
        };
        
        console.log(`[CHAT] Invio messaggio "${sanitizedMessage}" nella partita ${data.gameId} da ${playerName}`);
        
        // Primo, invia al mittente (ottimistic UI)
        socket.emit('chatMessage', chatMessageData);
        
        // Usa socket rooms per inviare a tutti gli altri nella partita
        socket.to(`game:${data.gameId}`).emit('chatMessage', chatMessageData);
        
        console.log(`[CHAT] ✅ Messaggio inviato a tutti i giocatori nella partita ${data.gameId}`);
    });
    
    socket.on('initialCardAnimationStarted', (data) => {
        // console.log(`[ANIMATION] Client ${socket.id} ha iniziato l'animazione della carta iniziale`);
        
        if (data.gameId) {
            // Invia i dati dell'animazione all'altro client
            socket.to(`game:${data.gameId}`).emit('otherClientInitialCardAnimation', {
                clientId: socket.id,
                clientMinimized: data.clientMinimized || false,
                card: data.card,
                position: data.position
            });
            
            // console.log(`[ANIMATION] Notificata animazione carta iniziale agli altri client per il gioco ${data.gameId}`);
        }
    });
    
    socket.on('battleStartAnimationStarted', (data) => {
        // console.log(`[ANIMATION] Client ${socket.id} ha iniziato l'animazione di battaglia`);
        
        if (data.gameId) {
            // Invia i dati dell'animazione all'altro client
            socket.to(`game:${data.gameId}`).emit('otherClientBattleAnimation', {
                clientId: socket.id,
                clientMinimized: data.clientMinimized || false,
                player1Name: data.player1Name,
                player2Name: data.player2Name
            });
            
            // console.log(`[ANIMATION] Notificata animazione battaglia agli altri client per il gioco ${data.gameId}`);
        }
    });
    
    socket.on('cardDealingAnimationStarted', (data) => {
        // console.log(`[ANIMATION] Client ${socket.id} ha iniziato l'animazione di distribuzione carte`);
        
        if (data.gameId) {
            // Invia i dati dell'animazione all'altro client
            socket.to(`game:${data.gameId}`).emit('otherClientCardDealingAnimation', {
                clientId: socket.id,
                clientMinimized: data.clientMinimized || false,
                playersState: data.playersState
            });
            
            // console.log(`[ANIMATION] Notificata animazione distribuzione carte agli altri client per il gioco ${data.gameId}`);
        }
    });

    socket.on('drawCard', ({ playerId }) => {
        // console.log(`Giocatore ${playerId} vuole pescare una carta.`);
        const gameId = playerGameMap[socket.id];

        if (!gameId) {
            console.error(`[GAME ERROR] Nessuna partita mappata per il socket ${socket.id} durante drawCard`);
            // Non invio l'errore perché potrebbe interferire durante la fase di match found
            // socket.emit('gameError', 'Nessuna partita attiva trovata. Prova a tornare al menu principale e iniziare una nuova partita.');
            return;
        }

        if (!activeGames[gameId]) {
            console.error(`[GAME ERROR] Partita ${gameId} non trovata per il socket ${socket.id} durante drawCard`);
            // Pulisci la mappatura errata
            delete playerGameMap[socket.id];
            // Non invio l'errore perché potrebbe interferire durante la fase di match found
            // socket.emit('gameError', 'La partita non è più attiva. Prova a iniziare una nuova partita.');
            return;
        }

        const game = activeGames[gameId];
        if (game.mode === 'local') {
            // Converti l'ID del giocatore
            const actualPlayerId = convertToLogicalPlayerId(socket.id, playerId, game);
            const result = game.handleDrawCard(actualPlayerId);
            if (!result.success) {
                socket.emit('gameError', result.reason || 'Errore nel pescare la carta.');
                return;
            }
            // Invia lo stato aggiornato
            socket.emit('gameState', game.getState(null, true));

            // Invia gli aggiornamenti di rating se la partita è terminata
            if (game.gameOver) {
                const ratingUpdates = game.updateRatings(game.winner);
                if (ratingUpdates) {
                    // console.log('DEBUG: Invio gameOver con ratingUpdates', ratingUpdates);

                    // Aggiorna i rating persistenti
                    if (ratingUpdates.winner) {
                        const winnerId = game.winner;
                        playerRatings[winnerId] = {
                            rating: ratingUpdates.winner.newRating,
                            playerName: game.playerNames[winnerId]
                        };
                    }

                    if (ratingUpdates.loser) {
                        const loserId = game.playerOrder.find(id => id !== game.winner);
                        playerRatings[loserId] = {
                            rating: ratingUpdates.loser.newRating,
                            playerName: game.playerNames[loserId]
                        };
                    }

                    socket.emit('gameOver', {
                        winnerId: game.winner,
                        ratingUpdates: ratingUpdates
                    });
                }
            }

            // Se è il turno dell'AI, esegui la sua mossa
            handleAiTurn(game, socket);
        } else { // Modalità AI o online
            handleGameAction(socket, (game, logicalPlayerId) => game.handleDrawCard(logicalPlayerId), playerId);
        }
    });

     socket.on('passTurn', ({ playerId }) => { // Aspetta playerId
         handleGameAction(socket, (game, logicalPlayerId) => {
             if (game.getCurrentPlayer()?.id !== logicalPlayerId) return { success: false, reason: "Non è il turno di questo giocatore logico." };
             // TODO: Verifica se il passaggio è valido
             game.logMessage(`${logicalPlayerId} passa il turno.`);
             game.nextTurn();
             return { success: true }; // Ritorna successo per aggiornare stato
         }, playerId);
     });
     
    // Gestione progresso animazioni per sincronizzare stati tra client
    socket.on('animationProgress', (data) => {
        // console.log(`[ANIMATION PROGRESS] Client ${socket.id} riporta progresso animazione: ${data.type} - ${data.progress}`);
        
        if (!data.gameId) return;
        
        const gameState = {
            type: data.type,
            progress: data.progress,
            clientId: socket.id,
            clientHidden: data.clientHidden || false,
            timestamp: Date.now()
        };
        
        // Aggiungi dati specifici per tipo di animazione
        switch(data.type) {
            case 'cardPlacement':
                gameState.playerIndex = data.playerIndex;
                gameState.cardIndex = data.cardIndex;
                break;
            case 'cardDealing':
                gameState.dealingProgress = data.dealingProgress;
                break;
        }
        
        // Notifica gli altri client nella stessa partita del progresso
        socket.to(`game:${data.gameId}`).emit('otherClientAnimationProgress', gameState);
        
        // Se il client è nascosto e l'animazione è completata, forza sincronizzazione
        if (data.clientHidden && data.progress === 'completed') {
            // console.log(`[ANIMATION PROGRESS] Client nascosto ${socket.id} ha completato animazione ${data.type}`);
            // Invia segnale di sincronizzazione a tutti i client
            io.to(`game:${data.gameId}`).emit('syncAnimationState', {
                type: data.type,
                clientId: socket.id,
                timestamp: Date.now()
            });
        }
    });

});

// --- Funzioni Helper ---

/**
 * Gestisce un'azione di gioco richiesta da un socket.
 * @param {socketIo.Socket} socket Il socket del giocatore.
 * @param {function(Game, string): {success: boolean, reason?: string}} actionFn La funzione da eseguire sull'istanza del gioco, passando l'ID logico.
 * @param {string} [clientPlayerId] L'ID logico fornito dal client (per partite locali).
 */
function handleGameAction(socket, actionFn, clientPlayerId = null) {
    const gameId = playerGameMap[socket.id];
    if (!gameId) {
        console.error(`[GAME ERROR] Nessuna partita mappata per il socket ${socket.id}`);
        // Non invio l'errore perché potrebbe interferire durante la fase di match found
        // socket.emit('gameError', 'Nessuna partita attiva trovata. Prova a tornare al menu principale e iniziare una nuova partita.');
        return;
    }

    if (!activeGames[gameId]) {
        console.error(`[GAME ERROR] Partita ${gameId} non trovata per il socket ${socket.id}`);
        // Pulisci la mappatura errata
        delete playerGameMap[socket.id];
        // Non invio l'errore perché potrebbe interferire durante la fase di match found
        // socket.emit('gameError', 'La partita non è più attiva. Prova a iniziare una nuova partita.');
        return;
    }

    const game = activeGames[gameId];

    if (game.gameOver) {
        socket.emit('gameError', 'La partita è già terminata.');
        return;
    }

    // Determina l'ID giocatore logico corretto
    const currentPlayerId = game.getCurrentPlayer()?.id;
    let logicalPlayerId;

    if (game.mode === 'local') {
        // In locale, l'azione è per il giocatore logico corrente,
        // ma verificata rispetto all'ID inviato dal client (se presente)
        // o semplicemente si assume che sia per il giocatore corrente.
        logicalPlayerId = currentPlayerId;
        // Potremmo aggiungere un controllo: if (clientPlayerId && clientPlayerId !== logicalPlayerId) ...
    } else { // Modalità AI
        logicalPlayerId = socket.id; // L'ID del socket è l'ID logico
    }

    if (!logicalPlayerId) {
         socket.emit('gameError', 'Impossibile determinare il giocatore logico.');
         return;
    }

     // Verifica turno (ora fatto dentro le funzioni handle... in Game.js)
    // if (currentPlayerId !== logicalPlayerId && currentPlayerId !== 'ai_player') {
    //     socket.emit('gameError', 'Non è il turno di questo giocatore.');
    //     return;
    // }

    // Esegui l'azione passando l'ID del giocatore logico
    const result = actionFn(game, logicalPlayerId);

    if (result.success) {
        // console.log(`Azione valida per ${socket.id} nel gioco ${gameId}.`);
        // Invia lo stato aggiornato a tutti i giocatori nella partita
        emitGameStateToPlayers(gameId);

        // Se la partita è finita, pulisci la mappa (ma non activeGames ancora, per permettere visualizzazione finale)
        if (game.gameOver) {
             game.playerOrder.forEach(pid => {
                 if (pid !== 'ai_player') delete playerGameMap[pid];
             });
             // Potremmo rimuovere activeGames[gameId] dopo un timeout?
             // console.log(`Gioco ${gameId} terminato.`);
        }
        // Se è il turno dell'AI dopo la mossa del giocatore
        else if (game.getCurrentPlayer()?.id === 'ai_player') {
            triggerAiMove(game);
        }

    } else {
        // console.log(`Azione non valida per ${socket.id} nel gioco ${gameId}: ${result.reason}`);

        // Gestione speciale per l'errore di limite carte
        if (result.errorType === "MAX_HAND_SIZE_REACHED") {
            // console.log(`Errore di limite carte per ${socket.id}: non passa il turno`);
            // Invia solo il messaggio di errore, senza aggiornare lo stato del gioco
            socket.emit('invalidMove', result.reason);

            // Non inviare l'evento showGameMessage, ora il messaggio viene gestito direttamente sul client

            // Invia un aggiornamento dello stato del gioco a tutti i giocatori
            // ma senza cambiare il turno
            emitGameStateToPlayers(gameId);
        }
        // Gestione speciale per il caso di ribaltone fallito
        else if (result.ribaltoneFailedWin) {
            // console.log(`Tentativo di ribaltone fallito per ${socket.id}: l'avversario vince`);
            // Invia il messaggio di errore
            socket.emit('invalidMove', result.reason);

            // Invia lo stato aggiornato che mostrerà la vittoria dell'avversario
            emitGameStateToPlayers(gameId);
        } else {
            // Altri tipi di errori
            socket.emit('invalidMove', result.reason || 'Mossa non valida.');
        }
    }
}

/**
 * Invia lo stato aggiornato del gioco a tutti i giocatori connessi in quella partita.
* @param {string} gameId
* @param {object} gameState (optional) - if not passed, will call game.getState()
*/
function emitGameStateToPlayers(gameId, gameState = null) {
   const game = activeGames[gameId];
   if (!game) return;

    if (game.mode === 'local') {
        // Invia stato completo al singolo socket host, indicando che è locale
        const playerSocket = io.sockets.sockets.get(game.hostSocketId);
        if (playerSocket) {
            // Passa null come requestingPlayerId per ottenere lo stato completo per la vista locale
            const state = game.getState(null, true); // Passa isLocal = true

            // Verifica se ci sono loop attivi
            if (state.loops && state.loops.length > 0) {
                // console.log(`[Server] Partita ${gameId} ha ${state.loops.length} loop attivi:`, JSON.stringify(state.loops));
            } else {
                // console.log(`[Server] Partita ${gameId} non ha loop attivi.`);
            }

            playerSocket.emit('gameState', state);
            // console.log(`Stato gioco locale ${gameId} inviato a ${game.hostSocketId}.`);
        } else {
             console.error(`Socket host ${game.hostSocketId} non trovato per partita locale ${gameId}`);
             // Potremmo voler rimuovere la partita qui se il socket non c'è più
             // delete activeGames[gameId];
             // delete playerGameMap[game.hostSocketId]; // Rimuovi mapping se necessario
        }
    } else {
        // Modalità AI o PvP Online (se reimplementata)
        game.playerOrder.forEach(playerId => {
            if (playerId !== 'ai_player') {
                // Per le partite multiplayer, il playerId potrebbe essere un socket ID vecchio
                // Prova prima con il socket ID, poi cerca per gameId
                let playerSocket = io.sockets.sockets.get(playerId);
                
                // Se non troviamo il socket, potrebbe essere un socket ID obsoleto
                // Cerca il socket tramite il playerGameMap
                if (!playerSocket) {
                    // Cerca il socket corrente del giocatore
                    for (const [socketId, socket] of io.sockets.sockets) {
                        if (playerGameMap[socketId] === gameId) {
                            // Verifica se questo socket appartiene al giocatore giusto
                            const game = activeGames[gameId];
                            if (game && game.playerOrder.includes(socketId)) {
                                playerSocket = socket;
                                break;
                            }
                        }
                    }
                }
                
                if (playerSocket) {
                    playerSocket.emit('gameState', game.getState(playerId, false)); // isLocal = false
                } else {
                    console.log(`[WARNING] Non è stato possibile trovare il socket per il giocatore ${playerId} nella partita ${gameId}`);
                }
            }
        });
        // console.log(`Stato gioco ${gameId} inviato ai giocatori.`);
    }
}

/**
 * Attiva il calcolo e l'esecuzione della mossa dell'AI.
 * @param {Game} game L'istanza del gioco.
 */
function triggerAiMove(game) {
    if (game.gameOver || game.getCurrentPlayer()?.id !== 'ai_player') return;

    // console.log(`Gioco ${game.gameId}: Turno dell'AI (${game.getCurrentPlayer().color}). Calcolo mossa...`);
    const aiPlayerId = 'ai_player';
    // Passa una copia dello stato per evitare modifiche accidentali
    // const gameStateForAI = JSON.parse(JSON.stringify(game.getState(aiPlayerId))); // Deep copy? getState dovrebbe già dare una copia sicura
    const gameStateForAI = game; // Passa l'istanza, l'AI non dovrebbe modificarla direttamente

    // Simula un piccolo ritardo per l'AI
    setTimeout(() => {
        if (game.gameOver) return; // Controlla di nuovo se la partita è finita nel frattempo

        const aiMove = getAiMove(gameStateForAI, aiPlayerId, 3); // Usa difficoltà 3

        if (aiMove) {
            // console.log(`Gioco ${game.gameId}: AI (${game.getCurrentPlayer()?.color}) esegue ${aiMove.action}`);
            let result;
            switch (aiMove.action) {
                case 'placeCard':
                    result = game.handlePlaceCard(aiPlayerId, aiMove.card, aiMove.position);
                    break;
                case 'placeCardOnVertex':
                    result = game.handlePlaceCardOnVertex(aiPlayerId, aiMove.card, aiMove.vertexId);
                    break;
                case 'drawCard':
                    result = game.handleDrawCard(aiPlayerId);
                    break;
                case 'passTurn':
                     if (game.getCurrentPlayer()?.id === aiPlayerId) {
                         game.logMessage(`AI ${aiPlayerId} passa il turno.`);
                         game.nextTurn();
                         result = { success: true }; // Indica successo per aggiornare stato
                     } else {
                         result = { success: false, reason: "AI ha tentato di passare fuori turno?" };
                     }
                    break;
                default:
                    console.error(`Gioco ${game.gameId}: Azione AI non riconosciuta: ${aiMove.action}`);
                    result = { success: false, reason: 'Azione AI sconosciuta' };
            }

            if (result.success) {
                emitGameStateToPlayers(game.gameId);
                 // Controlla se la partita è finita dopo la mossa dell'AI
                 if (game.gameOver) {
                     game.playerOrder.forEach(pid => {
                         if (pid !== 'ai_player') delete playerGameMap[pid];
                     });
                     // console.log(`Gioco ${gameId} terminato dopo mossa AI.`);
                 } else if (game.getCurrentPlayer()?.id === 'ai_player') {
                     // Se è di nuovo il turno dell'AI (improbabile senza errori), evita loop infinito
                     console.error(`Gioco ${game.gameId}: Rilevato potenziale loop AI, turno non riattivato.`);
                 }
            } else {
                console.error(`Gioco ${game.gameId}: Mossa AI fallita - ${result.reason}. L'AI passa il turno.`);
                // Se la mossa AI fallisce, passa il turno per evitare blocchi
                 if (game.getCurrentPlayer()?.id === aiPlayerId && !game.gameOver) {
                    game.logMessage(`AI ${aiPlayerId} non è riuscita a eseguire la mossa (${result.reason}) e passa.`);
                    game.nextTurn();
                    emitGameStateToPlayers(game.gameId);
                 }
            }
        } else {
            console.error(`Gioco ${game.gameId}: AI non è riuscita a determinare una mossa. Passa il turno.`);
             if (game.getCurrentPlayer()?.id === aiPlayerId && !game.gameOver) {
                 game.logMessage(`AI ${aiPlayerId} non ha trovato mosse e passa.`);
                 game.nextTurn();
                 emitGameStateToPlayers(game.gameId);
             }
        }

    }, 1000); // Ritardo di 1 secondo
}

// Funzione utility per convertire l'ID del socket all'ID logico del giocatore
function convertToLogicalPlayerId(socketId, playerId, game) {
    if (game.mode !== 'local') return socketId;

    console.log(`[convertToLogicalPlayerId] Input: socketId=${socketId}, playerId=${playerId}, mode=${game.mode}`);
    console.log(`[convertToLogicalPlayerId] localPlayerIds:`, game.localPlayerIds);
    console.log(`[convertToLogicalPlayerId] Player colors:`, game.playerColors);

    // In modalità locale, dobbiamo mappare correttamente il playerId ricevuto dal client
    // al playerId logico usato dal server
    if (game.localPlayerIds && game.localPlayerIds.length === 2) {
        // Se il playerId passato dal client corrisponde a uno degli ID logici, usalo
        if (game.localPlayerIds.includes(playerId)) {
            console.log(`[convertToLogicalPlayerId] Found exact match: ${playerId}`);
            return playerId;
        }
        
        // Altrimenti, mappa in base al colore (white = p1, black = p2)
        const player1Id = game.localPlayerIds[0]; // Sempre white
        const player2Id = game.localPlayerIds[1]; // Sempre black
        
        console.log(`[convertToLogicalPlayerId] player1Id=${player1Id} (${game.playerColors[player1Id]}), player2Id=${player2Id} (${game.playerColors[player2Id]})`);
        
        // Se il playerId contiene info del colore, usa quello
        if (playerId && playerId.includes('white')) {
            console.log(`[convertToLogicalPlayerId] Found 'white' in playerId, returning player1Id: ${player1Id}`);
            return player1Id;
        } else if (playerId && playerId.includes('black')) {
            console.log(`[convertToLogicalPlayerId] Found 'black' in playerId, returning player2Id: ${player2Id}`);
            return player2Id;
        }
        
        // Se il playerId termina con _p1 o _p2, usalo per la mappatura
        if (playerId && playerId.endsWith('_p1')) {
            console.log(`[convertToLogicalPlayerId] Found '_p1' suffix, returning player1Id: ${player1Id}`);
            return player1Id;
        } else if (playerId && playerId.endsWith('_p2')) {
            console.log(`[convertToLogicalPlayerId] Found '_p2' suffix, returning player2Id: ${player2Id}`);
            return player2Id;
        }
        
        // Altrimenti usa il giocatore di turno come fallback
        const currentPlayerId = game.getCurrentPlayer()?.id;
        console.log(`[convertToLogicalPlayerId] Using fallback currentPlayer: ${currentPlayerId}`);
        return currentPlayerId || player1Id;
    }

    // Fallback
    const fallback = game.localPlayerIds ? game.localPlayerIds[0] : socketId;
    console.log(`[convertToLogicalPlayerId] Final fallback: ${fallback}`);
    return fallback;
}

// Funzione per gestire il turno dell'AI
function handleAiTurn(game, socket) {
    // Se la partita è terminata, non fare nulla
    if (game.gameOver) return;

    // Se è il turno dell'AI dopo la mossa del giocatore
    if (game.getCurrentPlayer()?.id === 'ai_player') {
        setTimeout(() => {
            // Ottieni la mossa dell'AI
            const aiMove = getAiMove(game, 'ai_player', 2);
            if (!aiMove) {
                // console.log('Nessuna mossa valida per l\'AI');
                return;
            }

            // Esegui la mossa dell'AI
            let result;
            if (aiMove.action === 'placeCard') {
                result = game.handlePlaceCard('ai_player', aiMove.card, aiMove.position);
            } else if (aiMove.action === 'placeCardOnVertex') {
                result = game.handlePlaceCardOnVertex('ai_player', aiMove.card, aiMove.vertexId);
            } else if (aiMove.action === 'drawCard') {
                result = game.handleDrawCard('ai_player');
            } else if (aiMove.action === 'passTurn') {
                result = { success: true }; // Pass turn non ha un risultato specifico
                game.nextTurn();
            }

            // Log per debug rating
            // console.log('DEBUG: Controllo dati rating prima di invio gameState',
            //            Object.keys(game.playerRatings).length > 0 ? 'Rating presenti' : 'Rating NON presenti');

            // Ottieni lo stato del gioco compresi i rating
            const gameState = game.getState(null, true);
            // console.log('DEBUG: Ratings in gameState:', gameState.ratings ? 'presenti' : 'NON presenti');

            // Invia lo stato aggiornato
            socket.emit('gameState', gameState);

            // Invia gli aggiornamenti di rating se la partita è terminata
            if (game.gameOver) {
                const ratingUpdates = game.updateRatings(game.winner);
                if (ratingUpdates) {
                    // console.log('DEBUG: Invio gameOver con ratingUpdates', ratingUpdates);

                    // Aggiorna i rating persistenti
                    if (ratingUpdates.winner) {
                        const winnerId = game.winner;
                        playerRatings[winnerId] = {
                            rating: ratingUpdates.winner.newRating,
                            playerName: game.playerNames[winnerId]
                        };
                    }

                    if (ratingUpdates.loser) {
                        const loserId = game.playerOrder.find(id => id !== game.winner);
                        playerRatings[loserId] = {
                            rating: ratingUpdates.loser.newRating,
                            playerName: game.playerNames[loserId]
                        };
                    }

                    socket.emit('gameOver', {
                        winnerId: game.winner,
                        ratingUpdates: ratingUpdates
                    });
                }
            }
        }, 1000); // Ritardo di 1 secondo per l'AI
    }
}

// --- Scheduled Tasks ---

/**
 * Crea e avvia una partita tra due giocatori matchati
 */
async function createAndStartGame(player1, player2) {
    try {
        // Trova i socket correnti per i giocatori
        let player1Socket = null;
        let player2Socket = null;
        
        // Trova il socket attivo per ogni giocatore
        console.log(`[MATCHMAKING] Cercando socket per player1 (userId: ${player1.userId}) e player2 (userId: ${player2.userId})`);
        
        for (const [socketId, socket] of io.sockets.sockets) {
            if (socket.user && socket.user.id === player1.userId) {
                player1Socket = socket;
                player1.socketId = socketId; // Aggiorna il socket ID
                console.log(`[MATCHMAKING] Trovato socket per player1: ${socketId}`);
            }
            if (socket.user && socket.user.id === player2.userId) {
                player2Socket = socket;
                player2.socketId = socketId; // Aggiorna il socket ID
                console.log(`[MATCHMAKING] Trovato socket per player2: ${socketId}`);
            }
        }
        
        if (!player1Socket || !player2Socket) {
            console.error(`[MATCHMAKING] Could not find active sockets for players. Player1 socket: ${!!player1Socket}, Player2 socket: ${!!player2Socket}`);
            return null;
        }
        
        console.log(`[MATCHMAKING] Found active sockets - Player1: ${player1.socketId}, Player2: ${player2.socketId}`);
        
        // Crea una nuova partita
        const gameInfo = gameController.createGame({ isPrivate: false });
        const gameId = gameInfo.gameId;
        
        // Aggiungi i giocatori alla partita usando i socket ID corretti
        const player1Added = gameController.addPlayerToGame(gameId, player1.socketId, player1.username, player1.rating, player1.userId);
        const player2Added = gameController.addPlayerToGame(gameId, player2.socketId, player2.username, player2.rating, player2.userId);
        
        if (!player1Added || !player2Added) {
            console.error(`Failed to add players to game ${gameId}`);
            return null;
        }
        
        // UNISCI I SOCKET ALLA ROOM DELLA PARTITA
        player1Socket.join(`game:${gameId}`);
        player2Socket.join(`game:${gameId}`);
        console.log(`[MATCHMAKING] Player1 socket ${player1.socketId} joined room game:${gameId}`);
        console.log(`[MATCHMAKING] Player2 socket ${player2.socketId} joined room game:${gameId}`);
        
        // Aggiungi alla mappa utente-partita per gestire riconnessioni
        userGameMap[player1.userId] = gameId;
        userGameMap[player2.userId] = gameId;
        console.log(`[MATCHMAKING] Aggiunto a userGameMap: userId ${player1.userId} -> gameId ${gameId}`);
        console.log(`[MATCHMAKING] Aggiunto a userGameMap: userId ${player2.userId} -> gameId ${gameId}`);
        
        // Non chiamare startGame qui - verrà chiamato automaticamente da addPlayerToGame
        // quando il secondo giocatore si unisce
        console.log(`[MATCHMAKING] Game ${gameId} will start automatically when both players added`);
        
        // Verifica sincronizzazione activeGames tra server e controller
        const controllerGame = gameController.getGame(gameId);
        if (controllerGame && !activeGames[gameId]) {
            activeGames[gameId] = controllerGame;
            console.log(`[MATCHMAKING] Game ${gameId} synchronized from controller to server activeGames`);
        }
        
        // Log debug per verificare lo stato
        console.log(`[MATCHMAKING] Game ${gameId} in server activeGames: ${!!activeGames[gameId]}`);
        console.log(`[MATCHMAKING] Game ${gameId} in controller: ${!!controllerGame}`);
        console.log(`[MATCHMAKING] Game state started: ${controllerGame?.gameStarted}`);
        console.log(`[MATCHMAKING] Game players count: ${controllerGame ? Object.keys(controllerGame.players).length : 0}`);
        
        // Se il gioco non è ancora in activeGames, c'è un problema di sincronizzazione
        if (!activeGames[gameId]) {
            console.error(`[MATCHMAKING] ERROR: Game ${gameId} not synchronized! Forcing sync from controller`);
            if (controllerGame) {
                activeGames[gameId] = controllerGame;
                console.log(`[MATCHMAKING] Game ${gameId} forcefully synced`);
            } else {
                console.error(`[MATCHMAKING] CRITICAL ERROR: Cannot find game ${gameId} anywhere!`);
                return null;
            }
        }
        
        // Crea i dati del match per entrambi i giocatori
        const player1Data = {
            gameId: gameId,
            color: 'white',
            playerId: player1.socketId,  // Aggiungi l'ID del giocatore
            opponentId: player2.socketId,  // Aggiungi l'ID dell'avversario
            opponent: {
                id: player2.userId,
                name: player2.username,
                rating: player2.rating
            }
        };
        
        const player2Data = {
            gameId: gameId,
            color: 'black',
            playerId: player2.socketId,  // Aggiungi l'ID del giocatore
            opponentId: player1.socketId,  // Aggiungi l'ID dell'avversario
            opponent: {
                id: player1.userId,
                name: player1.username,
                rating: player1.rating
            }
        };
        
        // Invia l'evento matchFound a entrambi i giocatori usando i socket corretti
        player1Socket.emit('matchFound', player1Data);
        player2Socket.emit('matchFound', player2Data);
        
        console.log(`[MATCHMAKING] Match found event sent to both players for game ${gameId}`);
        console.log(`[MATCHMAKING] Player1 socket ID: ${player1.socketId}, connected: ${player1Socket.connected}`);
        console.log(`[MATCHMAKING] Player2 socket ID: ${player2.socketId}, connected: ${player2Socket.connected}`);
        
        // Invia lo stato iniziale del gioco
        const gameInstance = gameController.getGame(gameId);
        if (gameInstance) {
            emitGameStateToPlayers(gameId);
        }
        
        return gameInstance;
    } catch (error) {
        console.error('[MATCHMAKING] Error creating and starting game:', error);
        return null;
    }
}

/**
 * Esegue il matchmaking per i giocatori in attesa
 */
async function runMatchmaking() {
    try {
        const matchedPairs = await matchmakingController.findMatches();
        // Log solo quando troviamo effettivamente delle coppie
        if (matchedPairs.length > 0) {
            console.log(`[MATCHMAKING] Trovate ${matchedPairs.length} coppie.`);
        }

        for (const match of matchedPairs) {
            console.log(`[MATCHMAKING] Creazione partita per: ${match.players[0].username} vs ${match.players[1].username}`);
            const gameInstance = await createAndStartGame(match.players[0], match.players[1]);
            if (gameInstance) {
                console.log(`[MATCHMAKING] Partita ${gameInstance.id} creata e avviata con successo.`);
                // Notifica ai giocatori che il match è stato trovato e la partita sta iniziando
                // (createAndStartGame già invia 'matchFound')
            } else {
                console.log(`[MATCHMAKING] Fallimento nella creazione della partita per la coppia. Rimetti in coda?`);
                // Gestire il fallimento, ad esempio rimettendo i giocatori in coda o notificandoli.
                // Per ora, li lasciamo fuori dalla coda per evitare loop infiniti in caso di problemi persistenti.
                io.to(match.players[0].socketId).emit('matchmakingError', 'Errore durante la creazione della partita. Riprova.');
                io.to(match.players[1].socketId).emit('matchmakingError', 'Errore durante la creazione della partita. Riprova.');
            }
        }
    } catch (error) {
        console.error('[MATCHMAKING] Errore durante il ciclo di matchmaking:', error);
    }

    // Riesegui il matchmaking dopo un intervallo più frequente
    setTimeout(runMatchmaking, 2000); // Esegui ogni 2 secondi per una risposta più rapida
}

/**
 * Esegue la pulizia delle risorse
 */
async function runCleanup() {
    // console.log('[CLEANUP] Esecuzione pulizia periodica...');

    // 1. Pulisci le partite in activeGames che sono terminate da tempo o inattive
    const now = Date.now();
    const inactiveThreshold = 30 * 60 * 1000; // 30 minuti di inattività
    const completedThreshold = 5 * 60 * 1000; // 5 minuti dopo il completamento

    for (const gameId in activeGames) {
        const game = activeGames[gameId];
        let shouldDelete = false;

        if (game.isGameOver && (now - (game.lastMoveTime || game.startTime)) > completedThreshold) {
            // // console.log(`[CLEANUP] Partita completata ${gameId} da rimuovere.`);
            shouldDelete = true;
        } else if (!game.isGameOver && (now - (game.lastMoveTime || game.startTime)) > inactiveThreshold) {
            // // console.log(`[CLEANUP] Partita inattiva ${gameId} da rimuovere.`);
            // Potrebbe essere utile marcare la partita come abbandonata nel DB
            shouldDelete = true;
        }

        if (shouldDelete) {
            delete activeGames[gameId];
            // Rimuovi anche le mappature dei giocatori se ancora presenti (dovrebbero essere gestite da disconnect)
            for (const socketId in playerGameMap) {
                if (playerGameMap[socketId] === gameId) {
                    delete playerGameMap[socketId];
                }
            }
            // // console.log(`[CLEANUP] Partita ${gameId} rimossa da activeGames e playerGameMap.`);
        }
    }

    // 2. Pulisci la coda di matchmaking da giocatori disconnessi o inattivi da troppo tempo
    await matchmakingController.cleanupQueue();
    // // console.log('[CLEANUP] Coda di matchmaking pulita.');


    // 3. Pulisci le notificazioni vecchie
    notificationService.cleanupOldNotifications();
    // // console.log('[CLEANUP] Notifiche vecchie pulite.');

    // 4. Pulisci la cache
    cacheService.cleanup();
    // // console.log('[CLEANUP] Cache pulita.');


    // Riesegui il cleanup dopo un intervallo più lungo
    setTimeout(runCleanup, 15 * 60 * 1000); // Esegui ogni 15 minuti
}

// --- Avvio del server ---
async function startServer() {
    try {
        await createMultiplayerTables(); // Assicura che le tabelle esistano
        await addMoveNumberColumn(); // Assicura che la colonna esista
        // // console.log('Database tables checked/created successfully.');

        // Avvia il server HTTP e Socket.IO
        server.listen(PORT, () => {
            // // console.log(`Server Skèmino in ascolto sulla porta ${PORT}`);
            // // console.log(`Ambiente: ${process.env.NODE_ENV || 'development'}`);
            // // console.log('Accesso al gioco: http://localhost:3000');

            // Avvia il ciclo di matchmaking
            runMatchmaking();
            console.log('Matchmaking service started.');

            // Avvia il ciclo di cleanup
            runCleanup();
            // // console.log('Cleanup service started.');
        });

    } catch (error) {
        console.error('Failed to start the server:', error);
        process.exit(1); // Esci se il server non può avviarsi correttamente
    }
}

startServer();

module.exports = { app, server, io, activeGames, playerGameMap }; // Esporta per i test o altri moduli

> skeminogame@1.0.0 start
> node server/server.js

Using SQLite database at: E:\Progetto\Progetti\Gioco Sk├¿mino\skemino\data\skemino.db
[GAME CONTROLLER] Initialized with Socket.IO instance
[SERVER INIT] gameController inizializzato con Socket.IO per emettere eventi gameOver
[SERVER INIT] gameController pronto per gestire le partite online
Database initialized successfully
Database ready for use
Multiplayer tables created successfully
move_number column already exists in game_moves table
Matchmaking service started.
Cleaned up matchmaking queue: 0 DB entries, 0 memory entries
New socket connection: zPVpqVMDdG7WTD1hAAAB
User authenticated: giggio (2)
New socket connection: mDDQSzO38cvwf51UAAAD
User authenticated: brusco<PERSON> (1)

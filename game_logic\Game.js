const Deck = require('./Deck');
const Board = require('./Board');
const { Card } = require('./Card');

const MAX_PLAYERS = 2;
const INITIAL_HAND_SIZE = 5;
const MAX_HAND_SIZE = 10;

// Costanti per il sistema di rating ELO
const INITIAL_RATING = 1000;
const K_FACTOR = 32; // Fattore di impatto delle partite sul rating
const RATING_LEVELS = [
    { name: 'Principian<PERSON>', range: [1000, 1199], avatar: 'Principiante.webp' },
    { name: 'Dilettante Categoria D', range: [1200, 1399], avatar: 'Dilettante D.webp' },
    { name: 'Dilettante Categoria C', range: [1400, 1599], avatar: 'Dilettante C.webp' },
    { name: 'Dilettante Categoria B', range: [1600, 1799], avatar: 'Dilettante B.webp' },
    { name: 'Dilettante Categoria A', range: [1800, 1999], avatar: '<PERSON>lettante <PERSON><PERSON>webp' },
    { name: '<PERSON>di<PERSON><PERSON> Maestro', range: [2000, 2199], avatar: 'Candidato maestro.webp' },
    { name: '<PERSON><PERSON>', range: [2200, 2399], avatar: 'Maestro.webp' },
    { name: 'Maestro Internazionale', range: [2400, 2499], avatar: 'Maestro Internazionale.webp' },
    { name: 'Gran Maestro', range: [2500, 2699], avatar: 'Gran Maestro.webp' },
    { name: 'Super Gran Maestro', range: [2700, Infinity], avatar: 'Super Gran Maestro.webp' }
];

class Game {
    constructor(gameId, mode = 'ai') {
        this.gameId = gameId;
        this.mode = mode;
        this.hostSocketId = null;
        this.localPlayerIds = [];
        this.players = {};
        this.playerOrder = [];
        this.deck = new Deck();
        this.board = new Board();
        this.discardPile = [];
        this.currentPlayerIndex = 0;
        this.diceResult = null; // Per memorizzare risultato dadi
        this.gameStarted = false;
        this.gameOver = false;
        this.winner = null;
        this.winReason = '';
        this.log = [];
        this.playerColors = {};
        this.playerNames = {}; // Per memorizzare i nomi personalizzati dei giocatori
        this.playerRatings = {}; // Per memorizzare i rating dei giocatori
        this.continueForOneTurn = false; // Flag per ERA4 (turno successivo a ERA1)
        this.initialPosition = null; // Store initial position for dice animation
    }

    addPlayer(playerId, playerName = null, userId = null, forceColor = null) {
        console.log(`[Game ${this.gameId}] addPlayer chiamato con playerId: ${playerId}, playerName: ${playerName}, userId: ${userId}, forceColor: ${forceColor}`);
        console.log(`[Game ${this.gameId}] Giocatori esistenti prima dell'aggiunta:`, Object.keys(this.players));
        
        if (Object.keys(this.players).length >= MAX_PLAYERS) {
            console.warn("Massimo numero di giocatori raggiunto.");
            return false;
        }
        if (this.players[playerId]) {
            console.warn(`Giocatore ${playerId} già presente.`);
            return false;
        }
        // Per i giochi locali, assegna esplicitamente il colore basato sul suffisso
        let color;
        if (forceColor) {
            color = forceColor;
            console.log(`[Game ${this.gameId}] Forzando colore ${color} per ${playerId}`);
        } else if (this.mode === 'local' && playerId.endsWith('_p1')) {
            color = 'white';
            console.log(`[Game ${this.gameId}] Local game - Forzando ${playerId} come white (_p1)`);
        } else if (this.mode === 'local' && playerId.endsWith('_p2')) {
            color = 'black';
            console.log(`[Game ${this.gameId}] Local game - Forzando ${playerId} come black (_p2)`);
        } else {
            color = Object.keys(this.players).length === 0 ? 'white' : 'black';
        }
        console.log(`[Game ${this.gameId}] Assegno colore ${color} al giocatore ${playerId}`);
        
        this.players[playerId] = { 
            id: playerId, 
            socketId: playerId,
            userId: userId,
            color: color, 
            hand: [], 
            score: 0 
        };
        this.playerOrder.push(playerId);
        this.playerColors[playerId] = color;

        // Inizializza il rating del giocatore se non esiste già
        if (!this.playerRatings[playerId]) {
            // Per partite online, usa il rating di default (dovrebbe essere sovrascritto dal server)
            // Per partite locali, assegna rating fittizi basati sul colore per test
            if (this.mode === 'online') {
                this.playerRatings[playerId] = INITIAL_RATING; // 1000 di default
                console.log(`DEBUG: Rating di default assegnato a ${playerId} per partita online: ${this.playerRatings[playerId]}`);
            } else {
                // Per test locali, assegna rating fittizi basati sul colore
                const testRatings = {
                    'white': 1200, // Dilettante D
                    'black': 1600  // Dilettante B
                };
                this.playerRatings[playerId] = testRatings[color] || INITIAL_RATING;
                console.log(`DEBUG: Rating fittizio assegnato a ${playerId} (${color}) per partita locale: ${this.playerRatings[playerId]}`);
            }
        }

        // Memorizza il nome personalizzato del giocatore se fornito
        if (playerName) {
            this.playerNames[playerId] = playerName;
            this.logMessage(`Giocatore ${playerName} (${color}) si è unito.`);
        } else {
            // Usa un nome predefinito se non è fornito
            const defaultName = playerId === 'ai_player' ? 'Computer' : `Giocatore ${Object.keys(this.players).length}`;
            this.playerNames[playerId] = defaultName;
            this.logMessage(`Giocatore ${defaultName} (${color}) si è unito.`);
        }

        if (this.mode === 'ai' && Object.keys(this.players).length === 1) {
            this.addPlayer('ai_player', 'Computer', null, null);
        }
        return true;
    }

    removePlayer(playerId) {
        if (this.players[playerId]) {
            const color = this.players[playerId].color;
            this.logMessage(`Giocatore ${playerId} (${color}) si è disconnesso.`);
            delete this.players[playerId];
            this.playerOrder = this.playerOrder.filter(id => id !== playerId);
            delete this.playerColors[playerId];
            if (this.gameStarted && !this.gameOver) {
                const remainingPlayerId = this.playerOrder.find(id => id !== 'ai_player');
                this.endGame(remainingPlayerId || 'Nessuno', "L'avversario si è disconnesso.");
            }
            return true;
        }
        return false;
    }

    startGame() {
        if (this.gameStarted || Object.keys(this.players).length < MAX_PLAYERS) {
            console.warn("Impossibile avviare la partita: non ancora iniziata o giocatori insufficienti.");
            return { success: false, reason: "Giocatori insufficienti o partita già avviata." };
        }

        // Resetta il flag continueForOneTurn all'inizio di una nuova partita
        this.continueForOneTurn = false;
        this.logMessage(`[RIBALTONE INIT] Flag continueForOneTurn resettato a ${this.continueForOneTurn}`);

        this.diceResult = this.rollDice();
        const { alpha, numeric, color } = this.diceResult;
        const initialPosition = `${alpha}${numeric}`;
        this.initialPosition = initialPosition; // Store the initial position for the client
        this.logMessage(`Lancio dadi: Alpha=${alpha}, Numeric=${numeric}, Color=${color}. Posizione iniziale: ${initialPosition}`);

        // Riassegna i colori ai giocatori in base al risultato del dado colore
        // IMPORTANTE: Per giochi locali, mantieni i colori assegnati in addPlayer (p1=white, p2=black)
        if (this.playerOrder.length === 2) {
            const player1Id = this.playerOrder[0];
            const player2Id = this.playerOrder[1];
            
            // Per giochi locali, mantieni l'assegnazione originale dei colori
            if (this.mode === 'local') {
                this.logMessage(`[LOCAL GAME] Mantengo colori fissi: ${player1Id} = ${this.players[player1Id].color}, ${player2Id} = ${this.players[player2Id].color}`);
                // Trova chi è il giocatore bianco per l'ordine dei turni
                const whitePlayerId = this.players[player1Id].color === 'white' ? player1Id : player2Id;
                const blackPlayerId = this.players[player1Id].color === 'white' ? player2Id : player1Id;
                
                // Il giocatore bianco inizia sempre per primo
                const newPlayerOrder = [whitePlayerId, blackPlayerId];
                this.playerOrder = newPlayerOrder;
                this.currentPlayerIndex = 0;
                this.logMessage(`[LOCAL GAME] Riordinato array playerOrder: [${this.playerOrder.join(', ')}]`);
                this.logMessage(`[LOCAL GAME] Il giocatore bianco (${whitePlayerId}) inizia per primo.`);
            } else {
                // Per giochi non locali, usa il risultato del dado per assegnare i colori
                const whitePlayerId = color === 'white' ? player1Id : player2Id;
                const blackPlayerId = color === 'white' ? player2Id : player1Id;

                if (this.players[player1Id]) {
                    this.players[player1Id].color = color === 'white' ? 'white' : 'black';
                    this.playerColors[player1Id] = this.players[player1Id].color;
                }
                if (this.players[player2Id]) {
                    this.players[player2Id].color = color === 'white' ? 'black' : 'white';
                    this.playerColors[player2Id] = this.players[player2Id].color;
                }

                this.logMessage(`Assegnazione colori: ${this.playerNames[player1Id] || player1Id} è ${this.players[player1Id].color}, ${this.playerNames[player2Id] || player2Id} è ${this.players[player2Id].color}`);
                this.logMessage(`Il giocatore bianco (G1) inizia sempre per primo.`);

                const newPlayerOrder = [whitePlayerId, blackPlayerId];
                this.playerOrder = newPlayerOrder;
                this.currentPlayerIndex = 0;
                this.logMessage(`Riordinato array playerOrder: [${this.playerOrder.join(', ')}]`);
                this.logMessage(`Impostato giocatore bianco (${this.playerOrder[0]}) come primo giocatore.`);
                this.logMessage(`NOTA: Indipendentemente dal risultato del dado colore (${color}), il giocatore bianco inizia sempre per primo.`);
            }
        } else {
            this.logMessage("Errore: Numero di giocatori non valido.");
            return { success: false, reason: "Numero di giocatori non valido." };
        }

        const whitePlayerId = this.playerOrder[0];
        const blackPlayerId = this.playerOrder[1];
        if (!whitePlayerId || !blackPlayerId ||
            this.players[whitePlayerId]?.color !== 'white' ||
            this.players[blackPlayerId]?.color !== 'black') {
            this.logMessage("Errore: Mancano giocatori bianco o nero dopo la riassegnazione o i colori non sono corretti.");
            return { success: false, reason: "Errore nell'assegnazione dei colori ai giocatori." };
        }

        this.logMessage(`Inizio distribuzione carte...`);
        for (let i = 0; i < INITIAL_HAND_SIZE; i++) {
            this.logMessage(`--- Distribuzione ${i + 1}/${INITIAL_HAND_SIZE} ---`);
            this.drawCard(whitePlayerId);
            this.drawCard(blackPlayerId);
        }
        this.logMessage(`Fine distribuzione. Mani attuali:`);
        this.logMessage(`  ${whitePlayerId} (${this.playerNames[whitePlayerId] || 'Bianco'}): ${this.players[whitePlayerId]?.hand.map(c => c.toString()).join(', ')}`);
        this.logMessage(`  ${blackPlayerId} (${this.playerNames[blackPlayerId] || 'Nero'}): ${this.players[blackPlayerId]?.hand.map(c => c.toString()).join(', ')}`);

        const firstCard = this.deck.drawCard();
        if (firstCard) {
            const initialCardInstance = new Card(firstCard.suit, firstCard.value);
            initialCardInstance.ownerColor = 'neutral';
            this.board.grid[initialPosition] = initialCardInstance;
            this.board.initialCardPlaced = true;

            // Verifica se la posizione iniziale è un vertice e registralo come vertice di setup
            const vertexId = this.getVertexIdForPosition(initialPosition);
            if (vertexId) {
                this.board.setupVertices.add(vertexId);
                this.logMessage(`Carta iniziale ${initialCardInstance} piazzata in ${initialPosition} (da setup). Vertice ${vertexId} registrato come vertice di setup.`);
            } else {
                this.logMessage(`Carta iniziale ${initialCardInstance} piazzata in ${initialPosition} (da setup).`);
            }
        } else {
            this.logMessage("Errore: Mazzo vuoto durante il setup?");
            return { success: false, reason: "Mazzo vuoto durante setup." };
        }

        this.gameStarted = true;
        this.gameOver = false;
        const currentPlayer = this.getCurrentPlayer();
        this.logMessage(`Partita iniziata! Posizione iniziale: ${initialPosition}. Inizia il giocatore bianco (G1): ${this.playerNames[whitePlayerId] || whitePlayerId}. Turno di ${currentPlayer?.id} (${currentPlayer?.color}).`);
        return { success: true };
    }

    rollDice() {
        const alphaDice = ['a', 'b', 'c', 'd', 'e', 'f'];
        const numericDice = [1, 2, 3, 4, 5, 6];
        const colorDice = ['white', 'black'];
        const alphaResult = alphaDice[Math.floor(Math.random() * alphaDice.length)];
        const numericResult = numericDice[Math.floor(Math.random() * numericDice.length)];
        const colorResult = colorDice[Math.floor(Math.random() * colorDice.length)];
        return { alpha: alphaResult, numeric: numericResult, color: colorResult };
    }

    getCurrentPlayer() {
        const currentPlayerLogicalId = this.playerOrder[this.currentPlayerIndex];
        return this.players[currentPlayerLogicalId];
    }

    canPlayerMakeMove(playerId) {
        const player = this.players[playerId];
        if (!player || player.hand.length === 0) return false;
        for (const position in this.board.grid) {
            if (!this.board.isOccupied(position)) {
                for (const card of player.hand) {
                    const isLastCard = player.hand.length === 1;
                    if (this.board.isValidPlacement(card, position, player.color, isLastCard)) {
                        this.logMessage(`[Debug] Mossa valida trovata per ${playerId}: ${card} in ${position}`);
                        return true;
                    }
                }
            }
        }
        this.logMessage(`[Debug] Nessuna mossa valida trovata per ${playerId}`);
        return false;
    }

    getState(requestingPlayerId, isLocal = false) {
        console.log(`[Game.getState] Generazione stato per ${requestingPlayerId || 'tutti'}, isLocal=${isLocal}`);

        // ✅ PULIZIA CARTE DUPLICATE: Verifica e pulisci SEMPRE prima di generare lo stato
        this.cleanupDuplicateCards();

        // Trova i loop attivi prima di costruire lo stato
        const activeLoops = this.findActiveLoops();
        console.log(`[Game.getState] Trovati ${activeLoops.length} loop attivi:`, JSON.stringify(activeLoops));

        // Verifica se siamo nel turno di ERA4 (turno successivo a ERA1)
        let ribaltoneMessage = null;
        if (!this.gameOver && this.gameStarted) {
            // Log dettagliato per debug
            this.logMessage(`[RIBALTONE DEBUG] Stato continueForOneTurn: ${this.continueForOneTurn}`);
            this.logMessage(`[RIBALTONE DEBUG] Giocatore corrente: ${this.getCurrentPlayer()?.id} (${this.getCurrentPlayer()?.color})`);

            // Se siamo nel turno successivo a ERA1 (turno di ERA4), mostra sempre il messaggio
            if (this.continueForOneTurn === true) {
                ribaltoneMessage = "Ribaltone possibile?";
                this.logMessage(`[RIBALTONE CHECK] Siamo nel turno di ERA4 (turno successivo a ERA1). Mostro sempre il messaggio "Ribaltone possibile?"`);
                this.logMessage(`[RIBALTONE CHECK] Impostato ribaltoneMessage = "${ribaltoneMessage}"`);

                // Non resettiamo più il flag continueForOneTurn automaticamente
                // Sarà resettato solo quando il giocatore piazza una carta durante il turno di ERA4
                // o quando passa il turno
            }
        }

        const state = {
            gameId: this.gameId,
            mode: this.mode,
            players: {},
            board: this.board.grid,
            discardPile: this.discardPile,
            currentPlayerId: this.getCurrentPlayer()?.id,
            diceResult: this.diceResult,
            initialPosition: this.initialPosition, // Add initial position to state
            myPlayerId: requestingPlayerId,
            myColor: this.playerColors[requestingPlayerId],
            playerColors: this.playerColors,
            playerNames: this.playerNames,
            gameOver: this.gameOver,
            gameStarted: this.gameStarted,
            winner: this.winner,
            winReason: this.winReason,
            winCondition: this.winCondition, // Aggiungo il tipo di vittoria (ERA1, ERA2, ERA3, ERA4)
            deckSize: this.deck.remainingCards(), // Corretto da getSize a remainingCards
            currentTurnIndex: this.currentPlayerIndex,
            vertexControl: this.board.vertexControl,
            loops: activeLoops, // Usa i loop trovati in precedenza
            ratings: {}, // Aggiungo oggetto per i rating
            ribaltoneMessage: ribaltoneMessage, // Aggiungo il messaggio di ribaltone
            continueForOneTurn: this.continueForOneTurn, // Aggiungo flag per ERA4
            gameMessage: this.gameMessage, // Aggiungo eventuali messaggi di gioco
            errorMessage: this.errorMessage // Aggiungo eventuali messaggi di errore
        };

        // Resetta i messaggi di errore dopo averli inviati
        this.errorMessage = null;

        console.log(`[Game.getState] Stato generato con ${state.loops.length} loop`);

        // Log per debug del messaggio di ribaltone e flag continueForOneTurn
        if (ribaltoneMessage || this.continueForOneTurn) {
            this.logMessage(`[RIBALTONE STATE] Incluso ribaltoneMessage="${ribaltoneMessage}" nello stato inviato al client`);
            this.logMessage(`[RIBALTONE STATE] Incluso continueForOneTurn=${this.continueForOneTurn} nello stato inviato al client`);
            this.logMessage(`[RIBALTONE STATE] Stato completo: continueForOneTurn=${this.continueForOneTurn}, currentPlayerId=${this.getCurrentPlayer()?.id}`);
        }

        // Per ogni giocatore, includi le informazioni sulla mano se è il richiedente o se è una partita locale
        for (const playerId in this.players) {
            state.players[playerId] = {
                id: playerId,
                color: this.players[playerId].color,
                handSize: this.players[playerId].hand.length,
                score: this.players[playerId].score
            };

            // Includi sempre le mani complete per tutti i giocatori in questi casi:
            // 1. Se è una partita locale (this.mode === 'local')
            // 2. Se isLocal è true (usato per lo stato iniziale)
            // 3. Se è il giocatore richiedente (playerId === requestingPlayerId)
            // Modifica: Invia sempre la mano completa per rendere online identico a locale
            state.players[playerId].hand = this.players[playerId].hand.map(card => ({
                id: card.id,
                suit: card.suit,
                value: card.value
            }));

            // Log dettagliato per debug delle mani
            this.logMessage(`[HAND DEBUG] Incluse ${this.players[playerId].hand.length} carte per il giocatore ${playerId} (${this.players[playerId].color}) nello stato.`);
            this.logMessage(`[HAND DEBUG] Dettaglio carte per ${playerId}: ${JSON.stringify(this.players[playerId].hand.map(c => `${c.value}${c.suit}`))}`);

            // Aggiungi informazioni sul rating e livello del giocatore
            if (this.playerRatings[playerId]) {
                state.ratings[playerId] = {
                    rating: this.playerRatings[playerId],
                    level: this.getPlayerLevel(playerId)
                };
            }
        }

        if (this.diceResult && this.board.initialCardPlaced) {
            const initialPosition = `${this.diceResult.alpha}${this.diceResult.numeric}`;
            state.initialPosition = initialPosition;
            state.initialCard = this.board.grid[initialPosition] || null;
            if (!state.initialCard) {
                 console.error(`[Game ${this.gameId}] Errore in getState: initialCard non trovata in board.grid[${initialPosition}] nonostante initialCardPlaced sia true.`);
            }
        }



        return state;
    }

    /**
     * Crea lo stato del board per un giocatore specifico, aggiungendo flag per le carte dell'avversario
     */
    createBoardStateForPlayer(requestingPlayerId) {
        const boardState = {};
        const requestingPlayerColor = this.playerColors[requestingPlayerId];
        
        console.log(`[BOARD STATE DEBUG] Creating board state for ${requestingPlayerId} (${requestingPlayerColor})`);
        console.log(`[BOARD STATE DEBUG] Raw board.grid:`, JSON.stringify(this.board.grid));
        
        for (const position in this.board.grid) {
            const card = this.board.grid[position];
            if (card) {
                console.log(`[BOARD STATE DEBUG] Processing card at ${position}:`, card);
                boardState[position] = {
                    suit: card.suit,
                    value: card.value,
                    ownerColor: card.ownerColor,
                    placedByOpponent: card.ownerColor !== requestingPlayerColor
                };
                console.log(`[BOARD STATE DEBUG] Added to board state:`, boardState[position]);
            }
        }
        
        console.log(`[BOARD STATE DEBUG] Final board state for ${requestingPlayerId}:`, JSON.stringify(boardState));
        return boardState;
    }

    logMessage(message) {
        console.log(`[Game ${this.gameId}] ${message}`);
        this.log.push(message);
    }

    // ========================================================================
    // handlePlaceCard - VERSIONE CON LOGICA ERA1/ERA4 SECONDO FEEDBACK (v4 - ERA1+ERA4 continua)
    // ========================================================================
    handlePlaceCard(requestingPlayerId, cardData, position) {
        if (this.gameOver || !this.gameStarted) return { success: false, reason: "La partita è finita o non iniziata." };
        
        // Debug: verifica che stiamo guardando il giocatore giusto
        console.log(`[Game ${this.gameId}] handlePlaceCard - requestingPlayerId: ${requestingPlayerId}`);
        console.log(`[Game ${this.gameId}] Player info for requestingPlayerId:`, this.players[requestingPlayerId]);
        console.log(`[Game ${this.gameId}] Current player info:`, this.getCurrentPlayer());
        
        // Per i giochi locali, verifica che sia il turno corretto ma usa il giocatore richiesto
        let player;
        if (this.mode === 'local') {
            // Verifica che il requestingPlayerId esista
            player = this.players[requestingPlayerId];
            if (!player) {
                console.log(`[Game ${this.gameId}] Giocatore non trovato: ${requestingPlayerId}`);
                return { success: false, reason: "Giocatore non trovato." };
            }
            
            // Verifica che sia il turno del colore corretto
            const currentPlayer = this.getCurrentPlayer();
            if (currentPlayer && player.color !== currentPlayer.color) {
                console.log(`[Game ${this.gameId}] Local game - turno di ${currentPlayer.color}, ma richiesta da ${player.color}`);
                return { success: false, reason: "Non è il turno di questo giocatore." };
            }
            
            console.log(`[Game ${this.gameId}] Local game - usando il giocatore: ${requestingPlayerId} (${player.color})`);
        } else {
            const currentPlayer = this.getCurrentPlayer();
            if (!currentPlayer || requestingPlayerId !== currentPlayer.id) {
                console.log(`[Game ${this.gameId}] Tentativo mossa da ${requestingPlayerId}, ma turno di ${currentPlayer?.id}`);
                return { success: false, reason: "Non è il turno di questo giocatore." };
            }
            player = currentPlayer;
        }
        
        // Debug: log per vedere cosa arriva e cosa c'è nella mano
        console.log(`[Game ${this.gameId}] Carta ricevuta:`, cardData);
        console.log(`[Game ${this.gameId}] Tipo value ricevuto:`, typeof cardData.value);
        console.log(`[Game ${this.gameId}] Mano del giocatore ${player.id} (${player.color}):`, player.hand.map(c => ({
            id: c.id,
            suit: c.suit, 
            value: c.value,
            valueType: typeof c.value
        })));
        
        const cardIndex = player.hand.findIndex(c => c.suit === cardData.suit && c.value === cardData.value);
        if (cardIndex === -1) {
            console.log(`[Game ${this.gameId}] Carta non trovata. Confronto fallito:`);
            console.log(`[Game ${this.gameId}] Cercando: suit=${cardData.suit}, value=${cardData.value} (tipo: ${typeof cardData.value})`);
            return { success: false, reason: "Carta non trovata nella tua mano." };
        }

        const card = player.hand[cardIndex];
        let wasVertexConqueredOnLastMove = false; // Dichiarazione della variabile mancante
        const isLastCard = player.hand.length === 1; // Verifica se è l'ultima carta PRIMA di rimuoverla

        // --- NUOVA LOGICA DI GESTIONE RIBALTONE (ERA4) ---
        const isTargetCorner = this.board.isCornerPosition(position);
        const opponentColor = player.color === 'white' ? 'black' : 'white';
        const cardOnTarget = this.board.getCardAt(position); // Legge lo stato PRIMA del piazzamento

        // È un tentativo di Ribaltone? (Piazzamento su angolo occupato da avversario)
        if (isTargetCorner && cardOnTarget && cardOnTarget.ownerColor === opponentColor) {
            this.logMessage(`[HANDLE PLACE] Rilevato tentativo di Ribaltone su ${position} da ${player.id}`);
            // Verifica le condizioni specifiche del Ribaltone
            const isRibaltoneValid = this.checkTrueRibaltoneCondition(player, card, position);

            if (isRibaltoneValid) {
                // Ribaltone riuscito! Termina subito la partita.
                // Sovrascrivi la carta sul vertice (anche se Board.placeCard non lo fa in questo caso)
                const cardToPlace = new Card(card.suit, card.value);
                cardToPlace.ownerColor = player.color;
                this.board.grid[position] = cardToPlace; // Sovrascrittura manuale
                player.hand.splice(cardIndex, 1); // Rimuovi carta dalla mano
                // Ottieni il nome del giocatore invece dell'ID del socket
                const playerName = this.playerNames[player.id] || "Il giocatore";

                // Ottieni informazioni dettagliate sulle carte coinvolte
                const vertexCard = this.board.getCardAt(position);
                const adjacentCellPositions = this.getCellsAdjacentToVertex(this.getVertexIdForPosition(position));
                const adjacentCards = adjacentCellPositions
                    .filter(pos => this.board.isOccupied(pos))
                    .map(pos => ({ pos, card: this.board.getCardAt(pos) }));

                // Crea un messaggio più dettagliato
                const adjacentCardsText = adjacentCards.length > 0
                    ? ` e battendo ${adjacentCards.length === 1 ? 'la carta' : 'le carte'} ${adjacentCards.map(item => `${item.card} in ${item.pos.toUpperCase()}`).join(', ')}`
                    : '';

                this.logMessage(`${player.id} (${player.color}) esegue Ribaltone su ${position} con ${card}. Mano rimanente: ${player.hand.length}`);

                // Messaggio di vittoria più dettagliato
                const detailedReason = `ERA4 - Regola del Ribaltone: ${playerName} ha eseguito un ribaltone vincente piazzando ${card} sul vertice ${position.toUpperCase()}, battendo la carta ${vertexCard}${adjacentCardsText}.`;
                
                this.logMessage(`[RIBALTONE VICTORY] Chiamando endGame per vittoria ERA4: ${detailedReason}`);
                this.endGame(player.id, detailedReason);
                this.logMessage(`[RIBALTONE VICTORY] Dopo endGame: gameOver=${this.gameOver}, winner=${this.winner}`);
                
                return { success: true, isWin: true, winReason: 'ERA4' };
            } else {
                // Tentativo di Ribaltone fallito (non batte carta sul vertice o adiacenti)
                this.logMessage(`[HANDLE PLACE] Tentativo di Ribaltone su ${position} fallito. L'avversario vince la partita.`);

                // Trova l'ID dell'avversario
                const opponentId = this.playerOrder.find(id => id !== player.id);

                // Termina la partita con l'avversario come vincitore
                if (opponentId) {
                    // Ottieni il nome del giocatore invece dell'ID del socket
                    const playerName = this.playerNames[player.id] || "Il giocatore";

                    // Ottieni informazioni dettagliate sulle carte coinvolte
                    const vertexCard = this.board.getCardAt(position);
                    const adjacentCellPositions = this.getCellsAdjacentToVertex(this.getVertexIdForPosition(position));
                    const adjacentCards = adjacentCellPositions
                        .filter(pos => this.board.isOccupied(pos))
                        .map(pos => ({ pos, card: this.board.getCardAt(pos) }));

                    // Determina il motivo specifico del fallimento
                    let failureReason = "";
                    if (vertexCard && !card.winsAgainst(vertexCard)) {
                        failureReason = `la carta ${card} non batte la carta ${vertexCard} sul vertice ${position.toUpperCase()}`;
                    } else {
                        // Trova la prima carta adiacente che non viene battuta
                        const failingCard = adjacentCards.find(item => !card.winsAgainst(item.card));
                        if (failingCard) {
                            failureReason = `la carta ${card} non batte la carta ${failingCard.card} nella cella adiacente ${failingCard.pos.toUpperCase()}`;
                        } else {
                            failureReason = "la carta non batte tutte quelle richieste";
                        }
                    }

                    this.endGame(opponentId, `ERA4 - RIBALTONE FALLITO! ${playerName} ha tentato un ribaltone non valido su ${position.toUpperCase()} perché ${failureReason}.`);

                    // Restituisci success: false per far sì che il server mostri il messaggio di errore,
                    // ma includi ribaltoneFailedWin: true per indicare che l'avversario ha vinto
                    return {
                        success: false,
                        reason: `Tentativo di Ribaltone fallito: ${failureReason}.`,
                        ribaltoneFailedWin: true,
                        opponentId: opponentId
                    };
                } else {
                    // Caso improbabile: non c'è un avversario
                    return { success: false, reason: "Tentativo di Ribaltone fallito: la carta non batte quelle richieste." };
                }
            }
        }
        // --- FINE LOGICA RIBALTONE ---

        // Se non era un tentativo di Ribaltone, procedi con la logica di piazzamento normale...

        // Regola preliminare: l'ultima carta può andare solo su un vertice valido (ma non occupato dall'avversario, gestito sopra)
        if (isLastCard) {
            if (!isTargetCorner) { // isTargetCorner già calcolata
                this.logMessage(`${player.id} può giocare l'ultima carta solo su un vertice (a1, f1, a6, f6).`);
                return { success: false, reason: "Puoi giocare l'ultima carta solo su un vertice (a1, f1, a6, f6).", preventTurnChange: true };
            }
            // --- CONTROLLO ESCLUSIVA VERTICE RIMOSSO ---
            // La logica successiva in Board.js e la verifica ERA1 gestiranno
            // se il piazzamento è valido e se porta alla vittoria.
            // Controllo esistente (mantenuto): Non piazzare su vertice già controllato dallo stesso giocatore
            if (cardOnTarget && cardOnTarget.ownerColor === player.color) {
                 this.logMessage(`${player.id} non può piazzare l'ultima carta sul vertice ${position} già controllato.`);
                 return { success: false, reason: "Non puoi piazzare l'ultima carta su un vertice che già controlli.", preventTurnChange: true };
            }
        }

        this.logMessage(`[DEBUG PLACE] Tentativo di piazzare (normale) ${card} in ${position} da ${player.id}, isLastCard=${isLastCard}`);

        // Memorizza lo stato del controllo dei vertici adiacenti PRIMA del piazzamento
        const adjacentVerticesBeforePlace = this.getVerticesAdjacentToPosition(position);
        const previousVertexControlStatus = {};
        adjacentVerticesBeforePlace.forEach(vertexId => {
            previousVertexControlStatus[vertexId] = this.board.vertexControl[vertexId];
        });
        this.logMessage(`[DEBUG REVERSER PRE] Stato vertici adiacenti a ${position} prima del piazzamento: ${JSON.stringify(previousVertexControlStatus)}`);

        // Esegui il piazzamento sulla board (ora dovrebbe permettere il piazzamento su celle vuote)
        const placeResult = this.board.placeCard(card, position, player.color, isLastCard);
        this.logMessage(`[DEBUG PLACE] Dopo placeCard (normale): Success=${placeResult.success}, GainedControl=${placeResult.gainedControl || 'nessuno'}, Reason=${placeResult.reason || 'Nessuno'}`);

        if (!placeResult.success) {
            // Questo non dovrebbe accadere per celle vuote se non per regole di adiacenza/sconfitta/loop
            this.logMessage(`Mossa normale non valida per ${player.id}: ${placeResult.reason}`);
            return { success: false, reason: placeResult.reason };
        }

        // Piazzamento normale riuscito: rimuovi carta dalla mano e logga
        player.hand.splice(cardIndex, 1);
        this.logMessage(`${player.id} (${player.color}) piazza ${card} in ${position}. Mano rimanente: ${player.hand.length}`);
        
        // Debug: verifica che la carta sia effettivamente presente nella griglia
        const verifyCard = this.board.getCardAt(position);
        console.log(`[PLACE CARD VERIFY] Carta in ${position} dopo piazzamento:`, verifyCard);
        console.log(`[PLACE CARD VERIFY] Griglia completa:`, JSON.stringify(this.board.grid));

        let gameShouldEnd = false;
        let winReason = "";
        let winnerId = null;
        let continueForOneTurn = false;

        // --- LOGICA DI CONTROLLO VITTORIA POST-PIAZZAMENTO NORMALE ---

        // 1. Verifica ERA1 (Ultima carta su vertice valido/conquistato)
        let isEra1ConditionMet = false;
        let era1ReasonDetail = "";
        if (isLastCard && player.hand.length === 0) { // Verifica DOPO splice
            const vertexId = this.getVertexIdForPosition(position);
            if (vertexId) {
                // Leggi lo stato ATTUALE del vertice (dovrebbe essere stato aggiornato da Board.js se era null)
                const currentVertexController = this.board.vertexControl[vertexId];
                const wasAvailable = previousVertexControlStatus[vertexId] === null; // Controlla stato PRECEDENTE

                // ERA1 è soddisfatta se il vertice ora è del giocatore O se era già suo
                if (currentVertexController === player.color) {
                    isEra1ConditionMet = true;

                    // Ottieni il nome del giocatore invece dell'ID del socket
                    const playerName = this.playerNames[player.id] || "Il giocatore";

                    // Ottieni il nome del vertice in formato leggibile
                    const vertexName = vertexId.replace('vertex-', '').toUpperCase();

                    // Crea un messaggio più dettagliato
                    const detail = wasAvailable
                        ? `conquistando il vertice ${vertexName} che era libero`
                        : `sul vertice ${vertexName} già controllato dal giocatore`;

                    era1ReasonDetail = `ERA1 - Regola della conquista del vertice: ${playerName} ha piazzato la sua ultima carta (${card}) ${detail}.`;
                    this.logMessage(`[DEBUG ERA CHECK] Condizione ERA1 soddisfatta: ${era1ReasonDetail}`);

                    // Verifica se il gioco deve continuare (ERA1 + Conquista Vertice Libero)
                    if (wasAvailable) {
                         wasVertexConqueredOnLastMove = true; // Imposta flag per logica decisionale
                         this.logMessage(`[DEBUG ERA CHECK] Vertice ${vertexId} conquistato in questa mossa (era null).`);
                    }
                }
                 // Non serve controllare isAvailable qui, basta che ora sia del giocatore
            }
        }

        // 2. Verifica ERA4 originale (Ribaltone tramite celle centrali) - RIMOSSO CONTROLLO ERRATO
        // const reversedVertexId = this.checkReverserWinCondition(player, position, previousVertexControlStatus); // Rimosso: questa funzione controllava lo scenario errato
        // if (reversedVertexId) {
        //     this.logMessage(`[DEBUG ERA CHECK] Condizione ERA4 originale (Ribaltone da celle centrali) soddisfatta per vertice ${reversedVertexId}.`);
        // }


        // 3. Logica decisionale per fine partita
        if (isEra1ConditionMet) {
            // Condizione ERA1 soddisfatta. Controlla se l'avversario può fare Ribaltone.
            const opponentId = this.playerOrder.find(id => id !== player.id);
            const opponent = opponentId ? this.players[opponentId] : null;
            let opponentCanPotentiallyRibaltone = false;

            if (opponent && opponent.hand.length > 0) {
                const controlledVertices = Object.entries(this.board.vertexControl)
                                                .filter(([vId, controller]) => controller === player.color)
                                                .map(([vId]) => vId);
                this.logMessage(`[ERA1 OVERRIDE CHECK] Vertici controllati da ${player.color}: ${controlledVertices.join(', ') || 'Nessuno'}`);
                if (controlledVertices.length > 0) {
                    opponentCanPotentiallyRibaltone = opponent.hand.some(opponentCard =>
                        controlledVertices.some(vertexId => {
                            const vertexPosition = vertexId.split('-')[1];
                            this.logMessage(`[ERA1 OVERRIDE CHECK] Verifico se ${opponent.id} (${opponent.color}) può Ribaltone su ${vertexPosition} con ${opponentCard}`);
                            const canRibaltone = this.checkTrueRibaltoneCondition(opponent, opponentCard, vertexPosition);
                            if(canRibaltone) this.logMessage(`[ERA1 OVERRIDE CHECK] --> SI, ${opponentCard} può Ribaltone su ${vertexPosition}`);
                            return canRibaltone;
                        })
                    );
                    this.logMessage(`[ERA1 OVERRIDE CHECK RESULT] Esiste almeno una carta/vertice per Ribaltone avversario? ${opponentCanPotentiallyRibaltone}`);
                } else {
                     this.logMessage(`[ERA1 OVERRIDE CHECK] Nessun vertice controllato da ${player.color}, impossibile Ribaltone per avversario.`);
                }
            } else {
                 this.logMessage(`[ERA1 OVERRIDE CHECK] Avversario non ha carte, impossibile Ribaltone.`);
            }

            // Decisione finale per ERA1
            if (opponentCanPotentiallyRibaltone) {
                this.logMessage(`[ERA1 OVERRIDE] La partita continua per un turno perché l'avversario (${opponent.id}) ha ${opponent.hand.length} carte e una potenziale mossa di Ribaltone.`);
                continueForOneTurn = true; // Continua per un turno
                this.continueForOneTurn = true; // Imposta il flag nella classe
                this.logMessage(`[ERA1 OVERRIDE] Flag continueForOneTurn impostato a ${this.continueForOneTurn}`);

                // Imposta esplicitamente il messaggio di ribaltone
                this.gameMessage = "Ribaltone possibile?";
                this.logMessage(`[ERA1 OVERRIDE] Impostato gameMessage = "Ribaltone possibile?"`);

                // Log aggiuntivo per debug
                this.logMessage(`[ERA1 OVERRIDE DEBUG] Stato completo: continueForOneTurn=${this.continueForOneTurn}, gameMessage=${this.gameMessage}`);

                // Invia un messaggio di log al client per confermare che il flag è stato impostato
                this.logMessage(`[ERA1 OVERRIDE] ATTENZIONE: Sto impostando continueForOneTurn=true per il prossimo turno. Il client DEVE mostrare il messaggio "Ribaltone possibile?"`);
            } else {
                const reasonNoOverride = !opponent || opponent.hand.length === 0 ? "Avversario non ha carte" : "Nessuna carta avversaria valida per Ribaltone trovata";
                this.logMessage(`[WIN CONDITION] ERA1: Partita terminata. ${era1ReasonDetail}. L'avversario non può fare Ribaltone (${reasonNoOverride}).`);
                gameShouldEnd = true; // Imposta fine partita
                winReason = era1ReasonDetail;
                winnerId = player.id;
            }
        }
        // RIMOSSO else if (reversedVertexId) - La vittoria ERA4 è gestita solo dal blocco Ribaltone all'inizio della funzione

        // 4. Gestione fine partita o continuazione
        if (gameShouldEnd) { // Se una delle condizioni sopra ha impostato la fine
             if (continueForOneTurn) {
                 // Caso speciale ERA1 con possibile Ribaltone: non terminare, passa solo il turno
                 this.logMessage(`[GAME FLOW] Partita non terminata, continua per un turno (possibile Ribaltone).`);

                 // Imposta esplicitamente il messaggio di ribaltone prima di passare il turno
                 this.gameMessage = "Ribaltone possibile?";
                 this.logMessage(`[GAME FLOW] Impostato gameMessage = "Ribaltone possibile?" prima di passare il turno`);

                 this.nextTurn();
                 return { 
                     success: true,
                     gainedControl: placeResult.gainedControl
                 };
             } else {
                 // Fine partita immediata (ERA1 senza possibile Ribaltone, o ERA4 originale)
                 this.logMessage(`[GAME FLOW] Partita terminata immediatamente.`);
                 this.endGame(winnerId, winReason);
                 return { 
                     success: true, 
                     isWin: true,
                     gainedControl: placeResult.gainedControl
                 };
             }
        }

        // --- FINE LOGICA CONTROLLO VITTORIA ---

        // Log di conquista semplice (solo se non è ultima carta e non c'è stata vittoria)
        // Nota: placeResult.gainedControl si riferisce solo alla conquista da celle centrali
        if (placeResult.gainedControl && !isLastCard && !gameShouldEnd && !continueForOneTurn) {
             this.logMessage(`${player.id} (${player.color}) ha conquistato il vertice ${placeResult.gainedControl} (da cella centrale).`);
        }

        // Controllo ERA2: Plancia satura
        if (!this.gameOver && !gameShouldEnd && this.board.isFull()) { // Aggiunto !gameShouldEnd
            this.logMessage("La plancia è satura, nessuna altra carta può essere piazzata.");
            const blackPlayerId = this.playerOrder.find(id => this.players[id]?.color === 'black');
            const blackPlayer = blackPlayerId ? this.players[blackPlayerId] : null;

            // Il nero ha diritto all'ultimo turno se:
            // 1. È stato il bianco a giocare OPPURE siamo nel caso speciale ERA1+ERA4
            // 2. Il nero esiste e può giocare (pescare o muovere)
            const blackCanPlay = blackPlayer && (blackPlayer.hand.length < MAX_HAND_SIZE || this.canPlayerMakeMove(blackPlayerId));
            const blackGetsLastTurn = (player.color === 'white' || continueForOneTurn) && blackCanPlay;

            if (blackGetsLastTurn) {
                this.logMessage(`Plancia satura ${continueForOneTurn ? '(dopo caso ERA1+ERA4)' : 'dopo mossa del bianco'}. Il nero ha diritto all'ultimo turno.`);
                this.nextTurn();
                return { 
                    success: true,
                    gainedControl: placeResult.gainedControl
                };
            } else {
                 // Ottieni il nome del giocatore invece dell'ID del socket
                 const playerName = this.playerNames[player.id] || "Il giocatore";

                 // Crea un messaggio più dettagliato
                 const situazione = continueForOneTurn
                     ? '(dopo tentativo di ERA1 con possibile Ribaltone)'
                     : (player.color === 'black' ? 'dopo la mossa del giocatore nero' : 'dopo la mossa del giocatore bianco');

                 this.logMessage(`Plancia satura ${situazione}, e il nero non ha diritto/possibilità di ultimo turno. Calcolo punteggi.`);

                 // Messaggio di vittoria più dettagliato
                 const detailedReason = `ERA2 - Regola di saturazione del tabellone: ${playerName} ha completato la saturazione del tabellone ${situazione}. Non ci sono più celle libere per piazzare carte.`;
                 this.calculateScoresAndEndGame(detailedReason);
                 return { 
                     success: true,
                     gainedControl: placeResult.gainedControl
                 };
            }
        }

        // Se non ci sono altre condizioni di fine gioco (ERA1, ERA4, ERA2), passa al turno successivo
        // Questo include il caso ERA1+ERA4
        if (!this.gameOver) {
            this.nextTurn();
        }

        // Include gainedControl nel risultato per la sincronizzazione PSN
        return { 
            success: true,
            gainedControl: placeResult.gainedControl
        };
    }
    // ========================================================================
    // FINE handlePlaceCard
    // ========================================================================

    // ========================================================================
    // handleRibaltoneMove - Gestisce il piazzamento di una carta durante il turno di ERA4
    // ========================================================================
    handleRibaltoneMove(requestingPlayerId, cardData, position) {
        this.logMessage(`[RIBALTONE MOVE] Ricevuta richiesta di piazzamento Ribaltone da ${requestingPlayerId} in ${position}`);

        // Verifica se siamo nel turno di ERA4 (continueForOneTurn = true)
        if (!this.continueForOneTurn) {
            this.logMessage(`[RIBALTONE MOVE] Non siamo nel turno di ERA4 (continueForOneTurn = ${this.continueForOneTurn})`);
            return { success: false, reason: "Non siamo nel turno di ERA4" };
        }

        // Verifica se la posizione è un vertice
        const isCorner = this.board.isCornerPosition(position);
        if (!isCorner) {
            this.logMessage(`[RIBALTONE MOVE] La posizione ${position} non è un vertice`);
            return { success: false, reason: "La posizione non è un vertice" };
        }

        // Verifica se il vertice è occupato dall'avversario
        const cardOnTarget = this.board.getCardAt(position);
        const currentPlayer = this.getCurrentPlayer();
        const opponentColor = currentPlayer.color === 'white' ? 'black' : 'white';

        if (!cardOnTarget || cardOnTarget.ownerColor !== opponentColor) {
            this.logMessage(`[RIBALTONE MOVE] Il vertice ${position} non è occupato dall'avversario`);
            return { success: false, reason: "Il vertice non è occupato dall'avversario" };
        }

        // Esegui il piazzamento normale
        const result = this.handlePlaceCard(requestingPlayerId, cardData, position);

        this.logMessage(`[RIBALTONE MOVE DEBUG] Risultato handlePlaceCard: success=${result.success}, isWin=${result.isWin}, gameOver=${this.gameOver}`);

        // Se il piazzamento è riuscito, controlla se è stata una vittoria
        if (result.success) {
            if (result.isWin) {
                this.logMessage(`[RIBALTONE MOVE] Ribaltone riuscito! Vittoria per ${requestingPlayerId}`);
                // Non resettare il flag continueForOneTurn perché la partita è finita
                // Il flag gameOver è già stato impostato da handlePlaceCard -> endGame
                this.logMessage(`[RIBALTONE MOVE] Partita terminata con vittoria, gameOver = ${this.gameOver}`);
            } else {
                this.logMessage(`[RIBALTONE MOVE] Piazzamento riuscito ma senza vittoria, resetto flag continueForOneTurn`);
                this.continueForOneTurn = false;
                this.logMessage(`[RIBALTONE MOVE] Flag continueForOneTurn resettato a ${this.continueForOneTurn}`);
            }

            // Non impostare più un messaggio di gioco per il ribaltone completato
            // Lasciamo che rimanga solo il messaggio "Ribaltone possibile?" nel client
            this.logMessage(`[RIBALTONE MOVE] Non imposto più un messaggio di gioco per il ribaltone completato`);
        } else {
            this.logMessage(`[RIBALTONE MOVE] Piazzamento fallito: ${result.reason}`);
            // NON resettare il flag continueForOneTurn se il piazzamento fallisce
            // Questo assicura che il messaggio "Ribaltone possibile?" rimanga visibile
            this.logMessage(`[RIBALTONE MOVE] Mantengo flag continueForOneTurn = ${this.continueForOneTurn}`);
        }

        return result;
    }


    /**
     * Gestisce la richiesta di un giocatore di pescare una carta.
     */
    handleDrawCard(requestingPlayerId) {
        this.logMessage(`[HANDLE DRAW] Ricevuta richiesta draw da ${requestingPlayerId}`); // LOG 1: Entry point

        if (this.gameOver || !this.gameStarted) {
            this.logMessage(`[HANDLE DRAW] Bloccato: gameOver=${this.gameOver}, gameStarted=${this.gameStarted}`);
            return { success: false, reason: "La partita è finita o non iniziata." };
        }
        const currentPlayer = this.getCurrentPlayer();
         if (!currentPlayer || requestingPlayerId !== currentPlayer.id) {
             this.logMessage(`[HANDLE DRAW] Bloccato: Tentativo draw da ${requestingPlayerId}, ma turno di ${currentPlayer?.id}`);
            return { success: false, reason: "Non è il turno di questo giocatore." };
        }
        const player = currentPlayer;
        this.logMessage(`[HANDLE DRAW] Giocatore ${player.id} (${player.color}) è il giocatore di turno.`); // LOG 2: Player confirmed

        this.logMessage(`[HANDLE DRAW] Controllo mano: ${player.hand.length} carte vs MAX ${MAX_HAND_SIZE}`); // LOG 3: Hand size check
        if (player.hand.length >= MAX_HAND_SIZE) {
             this.logMessage(`[HANDLE DRAW] Bloccato: ${player.id} ha raggiunto il limite di carte (${MAX_HAND_SIZE}).`);
             return {
                success: false,
                reason: "NON PUOI PESCARE ALTRE CARTE",
                errorType: "MAX_HAND_SIZE_REACHED",
                preventTurnChange: true // Assicurati che il turno non cambi
             };
        }

        this.logMessage(`[HANDLE DRAW] Tentativo di pescare dal mazzo...`); // LOG 4: Attempting draw
        const drawnCard = this.drawCard(player.id); // drawCard già logga internamente

        if (drawnCard) {
            this.logMessage(`[HANDLE DRAW] Pesca riuscita: ${drawnCard}. Mano attuale: ${player.hand.length}`); // LOG 5: Draw success

            // Se siamo in un turno di ribaltone e il giocatore pesca, l'avversario vince per ERA1
            if (this.continueForOneTurn) {
                this.logMessage(`[HANDLE DRAW] Siamo in un turno di ribaltone (continueForOneTurn = ${this.continueForOneTurn})`);
                this.logMessage(`[HANDLE DRAW] Il giocatore ha pescato durante il turno di ribaltone, l'avversario vince per ERA1`);

                // Trova l'ID dell'avversario
                const opponentId = this.playerOrder.find(id => id !== player.id);
                if (opponentId) {
                    // Ottieni il nome del giocatore e dell'avversario
                    const playerName = this.playerNames[player.id] || "Il giocatore";
                    const opponentName = this.playerNames[opponentId] || "L'avversario";

                    // Crea un messaggio dettagliato per la vittoria
                    const era1ReasonDetail = `ERA1 - Vittoria durante il turno di ribaltone: ${playerName} ha pescato la carta ${drawnCard} invece di tentare un ribaltone. ${opponentName} vince la partita.`;

                    this.logMessage(`[HANDLE DRAW] Vittoria ERA1 assegnata all'avversario: ${era1ReasonDetail}`);

                    // Termina la partita con vittoria ERA1 per l'avversario
                    this.endGame(opponentId, era1ReasonDetail);
                    return {
                        success: true,
                        isWin: true,
                        winReason: 'ERA1',
                        winCondition: 'ERA1',
                        winner: opponentId
                    };
                } else {
                    this.logMessage(`[HANDLE DRAW] Errore: Impossibile trovare l'avversario per assegnare la vittoria ERA1`);
                    // Imposta esplicitamente il messaggio di ribaltone prima di passare il turno
                    this.gameMessage = "Ribaltone possibile?";
                }
            }

            this.logMessage(`[HANDLE DRAW] Chiamata nextTurn() dopo pesca...`); // LOG 6: Before nextTurn
            this.nextTurn();
            this.logMessage(`[HANDLE DRAW] Ritorno da nextTurn(). Invio stato aggiornato.`); // LOG 7: After nextTurn
            return { success: true };
        } else {
            this.logMessage("[HANDLE DRAW] Pesca fallita: Il mazzo è esaurito!"); // LOG 8: Deck empty
            // ERA3: Controllo per esaurimento del mazzo
            if (player.color === 'white') {
                const blackPlayerId = this.playerOrder.find(id => this.players[id]?.color === 'black');
                const blackPlayer = blackPlayerId ? this.players[blackPlayerId] : null;
                if (blackPlayer && this.canPlayerMakeMove(blackPlayerId)) {
                    this.logMessage("[HANDLE DRAW] ERA3: Mazzo esaurito dopo pescata del bianco. Il nero ha diritto all'ultimo turno.");
                    this.nextTurn();
                    return { success: true, reason: "Mazzo esaurito, il nero ha diritto all'ultimo turno." };
                }
            }
            // Ottieni il nome del giocatore invece dell'ID del socket
            const playerName = this.playerNames[player.id] || "Il giocatore";

            this.logMessage("[HANDLE DRAW] ERA3: Calcolo punteggi e fine partita per mazzo esaurito.");

            // Messaggio di vittoria più dettagliato
            const detailedReason = `ERA3 - Regola dell'esaurimento carte dal mazzo: ${playerName} ha pescato l'ultima carta disponibile. Il mazzo è ora esaurito e non ci sono più carte da pescare.`;
            this.calculateScoresAndEndGame(detailedReason);
            return { success: true, reason: "Mazzo esaurito, partita terminata." };
        }
    }

    drawCard(playerId) {
        const player = this.players[playerId];
        if (!player) return null;
        if (this.deck.isEmpty()) return null;
        const card = this.deck.drawCard();
        if (card) {
            player.hand.push(card);
            this.logMessage(`  -> ${playerId} pesca ${card.toString()}. Mano attuale: ${player.hand.length} carte.`);
        } else {
            this.logMessage(`  -> ${playerId} tenta di pescare ma il mazzo è vuoto.`);
        }
        return card;
    }

    nextTurn() {
        if (this.checkStallCondition()) {
            return;
        }

        // NON resettare il flag continueForOneTurn qui
        // Lasciamo che sia il client a gestire la visualizzazione del messaggio
        // e il server resetterà il flag solo quando il giocatore fa una mossa
        if (this.continueForOneTurn) {
            this.logMessage(`[RIBALTONE TURN] Flag continueForOneTurn = ${this.continueForOneTurn} durante il cambio di turno.`);
            this.logMessage(`[RIBALTONE TURN] Mantengo flag continueForOneTurn = true per assicurarmi che il messaggio rimanga visibile.`);

            // Ottieni il giocatore corrente prima di cambiare turno
            const currentPlayer = this.getCurrentPlayer();
            if (currentPlayer) {
                this.logMessage(`[RIBALTONE TURN] Giocatore ${currentPlayer.id} (${currentPlayer.color}) ha passato il turno durante un possibile ribaltone.`);
            }

            // NON resettare il flag qui
            // this.continueForOneTurn = false;
        }

        this.currentPlayerIndex = (this.currentPlayerIndex + 1) % this.playerOrder.length;
        const nextPlayer = this.getCurrentPlayer();
        if (nextPlayer) {
            this.logMessage(`Turno di ${nextPlayer.id} (${nextPlayer.color}).`);
        } else {
             this.logMessage(`Errore: Impossibile determinare il prossimo giocatore.`);
             if (this.playerOrder.length > 0) {
                 this.currentPlayerIndex = 0;
                 const fallbackPlayer = this.getCurrentPlayer();
                 this.logMessage(`Fallback: Turno di ${fallbackPlayer?.id} (${fallbackPlayer?.color}).`);
             } else {
                 this.logMessage("Nessun giocatore rimasto. Impossibile continuare.");
                 this.endGame(null, "Nessun giocatore rimasto.");
             }
        }
    }

    calculateScoresAndEndGame(reason) {
        let lowestScore = Infinity;
        let winnerId = null;
        for (const playerId of this.playerOrder) {
            const player = this.players[playerId];
            if (!player) continue;
            player.score = player.hand.reduce((sum, card) => sum + card.scoreValue, 0);
            this.logMessage(`Punteggio finale ${playerId}: ${player.score}`);
            if (player.score < lowestScore) {
                lowestScore = player.score;
                winnerId = playerId;
            } else if (player.score === lowestScore) { /* Pareggio gestito dal primo */ }
        }
        this.endGame(winnerId, `${reason} Vince chi ha il punteggio più basso (${lowestScore}).`);
    }

    // Calcola il livello di abilità in base al rating
    getPlayerLevel(playerId) {
        const rating = this.playerRatings[playerId] || INITIAL_RATING;
        console.log(`DEBUG getPlayerLevel: Calcolo livello per ${playerId} con rating ${rating}`);
        console.log(`DEBUG getPlayerLevel: RATING_LEVELS disponibili: ${RATING_LEVELS.length}`);

        for (const level of RATING_LEVELS) {
            console.log(`DEBUG getPlayerLevel: Controllo livello ${level.name}: ${level.range[0]}-${level.range[1]}`);
            if (rating >= level.range[0] && rating <= level.range[1]) {
                const result = {
                    name: level.name,
                    rating: rating,
                    avatar: level.avatar,
                    min: level.range[0],
                    max: level.range[1],
                    progress: (rating - level.range[0]) / (level.range[1] - level.range[0])
                };
                console.log(`DEBUG getPlayerLevel: Livello trovato: ${result.name}, progress: ${result.progress}`);
                return result;
            }
        }
        console.log(`DEBUG getPlayerLevel: Nessun livello trovato per rating ${rating}`);
        return null;
    }

    // Aggiorna i rating dopo una partita
    updateRatings(winnerId) {
        if (this.mode !== 'local' || this.playerOrder.length !== 2) return;

        const loserId = this.playerOrder.find(id => id !== winnerId);
        if (!loserId) return;

        const winnerRating = this.playerRatings[winnerId] || INITIAL_RATING;
        const loserRating = this.playerRatings[loserId] || INITIAL_RATING;

        // Calcola la probabilità attesa di vittoria
        const expectedWinner = 1 / (1 + Math.pow(10, (loserRating - winnerRating) / 400));
        const expectedLoser = 1 / (1 + Math.pow(10, (winnerRating - loserRating) / 400));

        // Aggiorna i rating
        this.playerRatings[winnerId] = Math.round(winnerRating + K_FACTOR * (1 - expectedWinner));
        this.playerRatings[loserId] = Math.round(loserRating + K_FACTOR * (0 - expectedLoser));

        this.logMessage(`Aggiornamento rating: ${this.playerNames[winnerId]} (${this.playerRatings[winnerId]}), ${this.playerNames[loserId]} (${this.playerRatings[loserId]})`);

        return {
            winner: {
                oldRating: winnerRating,
                newRating: this.playerRatings[winnerId],
                level: this.getPlayerLevel(winnerId)
            },
            loser: {
                oldRating: loserRating,
                newRating: this.playerRatings[loserId],
                level: this.getPlayerLevel(loserId)
            }
        };
    }

    endGame(winnerId, reason) {
        this.logMessage(`[END GAME] Chiamata endGame: winnerId=${winnerId}, reason=${reason}, gameOver attuale=${this.gameOver}`);
        
        if (this.gameOver) {
            this.logMessage(`[END GAME] Partita già terminata, esco dalla funzione`);
            return;
        }
        
        this.gameOver = true;
        this.winner = winnerId;
        this.winReason = reason;
        
        this.logMessage(`[END GAME] Stato impostato: gameOver=${this.gameOver}, winner=${this.winner}, winReason=${this.winReason}`);

        // Determina il tipo di vittoria (ERA1, ERA2, ERA3, ERA4) dal messaggio di ragione
        if (reason.includes('ERA1')) {
            this.winCondition = 'ERA1';
            this.logMessage(`[ENDGAME] Vittoria per ERA1 assegnata a ${winnerId}`);
        } else if (reason.includes('ERA2')) {
            this.winCondition = 'ERA2';
            this.logMessage(`[ENDGAME] Vittoria per ERA2 assegnata a ${winnerId}`);
        } else if (reason.includes('ERA3')) {
            this.winCondition = 'ERA3';
            this.logMessage(`[ENDGAME] Vittoria per ERA3 assegnata a ${winnerId}`);
        } else if (reason.includes('ERA4')) {
            this.winCondition = 'ERA4';
            this.logMessage(`[ENDGAME] Vittoria per ERA4 assegnata a ${winnerId}`);
        } else {
            this.winCondition = 'OTHER';
            this.logMessage(`[ENDGAME] Vittoria per altra ragione assegnata a ${winnerId}`);
        }

        // Aggiorna i rating dei giocatori se c'è un vincitore
        const ratingUpdates = winnerId ? this.updateRatings(winnerId) : null;

        if (winnerId) {
            this.logMessage(`Partita terminata! Vincitore: ${this.playerNames[winnerId] || winnerId}. Motivo: ${reason}`);
        } else {
            this.logMessage(`Partita terminata senza vincitore. Motivo: ${reason}`);
        }

        return ratingUpdates;
    }

    getVertexIdForPosition(position) {
        const colChar = position.charAt(0);
        const row = parseInt(position.substring(1));
        if (row === 1 && colChar === 'a') return 'vertex-a1';
        if (row === 1 && colChar === 'f') return 'vertex-f1';
        if (row === 6 && colChar === 'a') return 'vertex-a6';
        if (row === 6 && colChar === 'f') return 'vertex-f6';
        return null;
    }

   /**
    * Gestisce il piazzamento dell'ultima carta di un giocatore su un vertice controllato o disponibile.
    * DEPRECATO: La logica è ora integrata in handlePlaceCard.
    */
   handlePlaceCardOnVertex(requestingPlayerId, cardData, vertexId) {
       this.logMessage("[WARN] handlePlaceCardOnVertex è deprecato. La logica è in handlePlaceCard.");
       return { success: false, reason: "Funzione deprecata." };
   }

   /**
    * Verifica se c'è una condizione Reverser per il piazzamento su un vertice avversario.
    * DEPRECATO: La logica è ora integrata in checkReverserWinCondition.
    */
   checkReverserCondition(player, vertexId) {
        this.logMessage("[WARN] checkReverserCondition è deprecato. La logica è in checkReverserWinCondition.");
        return false;
   }

   /**
    * Restituisce i vertici adiacenti a una data posizione.
    */
   getVerticesAdjacentToPosition(position) {
       const colChar = position.charAt(0);
       const row = parseInt(position.substring(1));
       const adjacentVertices = [];
       if (colChar === 'a' || colChar === 'b') {
           if (row === 1 || row === 2) adjacentVertices.push('vertex-a1');
           if (row === 5 || row === 6) adjacentVertices.push('vertex-a6');
       }
       if (colChar === 'e' || colChar === 'f') {
           if (row === 1 || row === 2) adjacentVertices.push('vertex-f1');
           if (row === 5 || row === 6) adjacentVertices.push('vertex-f6');
       }
       return adjacentVertices;
   }

   /**
    * Restituisce le celle adiacenti a un dato vertice.
    */
   getCellsAdjacentToVertex(vertexId) {
       switch (vertexId) {
           case "vertex-a1": return ["a2", "b1"];
           case "vertex-f1": return ["e1", "f2"];
           case "vertex-a6": return ["a5", "b6"];
           case "vertex-f6": return ["e6", "f5"];
           default: return [];
       }
   }

    /**
     * Controlla se la partita è in una condizione di stallo.
     */
    checkStallCondition() {
        if (this.gameOver) return false;
        const initialPlayerIndex = this.currentPlayerIndex;
        let playersChecked = 0;
        let canAnyoneMoveOrDraw = false;
        while (playersChecked < this.playerOrder.length) {
            const playerToCheckIndex = (initialPlayerIndex + playersChecked) % this.playerOrder.length;
            const playerId = this.playerOrder[playerToCheckIndex];
            const player = this.players[playerId];
            if (player) {
                const hasMoves = this.canPlayerMakeMove(playerId);
                const canDraw = !this.deck.isEmpty() && player.hand.length < MAX_HAND_SIZE;
                if (hasMoves || canDraw) {
                    canAnyoneMoveOrDraw = true;
                    break;
                }
            }
            playersChecked++;
        }
        if (!canAnyoneMoveOrDraw && this.gameStarted) {
            this.logMessage("Condizione di stallo rilevata: nessun giocatore può fare mosse valide o pescare.");
            this.calculateScoresAndEndGame("Stallo - Nessun giocatore può muovere.");
            return true;
        }
        return false;
    }

    /**
     * Trova tutti i loop attivi sulla plancia di gioco.
     */
    findActiveLoops() {
        console.log('[Game.findActiveLoops] INIZIO ricerca loop attivi...');
        const loops = [];

        // Conta le celle vuote e occupate per debug
        let emptyCells = 0;
        let occupiedCells = 0;
        for (const position in this.board.grid) {
            if (this.board.isOccupied(position)) {
                occupiedCells++;
            } else {
                emptyCells++;
            }
        }
        console.log(`[Game.findActiveLoops] Stato plancia: ${occupiedCells} celle occupate, ${emptyCells} celle vuote`);

        // Cerca loop in ogni cella vuota
        for (const position in this.board.grid) {
            if (!this.board.isOccupied(position)) {
                const adjacentPositions = this.board.getAdjacentPositions(position);
                const adjacentCards = adjacentPositions
                    .map(pos => ({ pos, card: this.board.getCardAt(pos) }))
                    .filter(item => item.card)
                    .map(item => item.card);

                console.log(`[Game.findActiveLoops] Posizione ${position} ha ${adjacentCards.length} carte adiacenti. Verifico loop...`);

                // Verifica se ci sono abbastanza carte adiacenti per un potenziale loop
                if (adjacentCards.length >= 3) {
                    console.log(`[Game.findActiveLoops] Posizione ${position} ha almeno 3 carte adiacenti, potenziale loop.`);

                    // Conta i simboli unici per debug
                    const uniqueSuits = new Set(adjacentCards.map(card => card.suit));
                    console.log(`[Game.findActiveLoops] Posizione ${position} ha ${uniqueSuits.size} simboli unici: ${Array.from(uniqueSuits).join(', ')}`);

                    // Verifica se c'è un loop
                    const testCard = { suit: 'Rock', value: '7', winsAgainst: () => false }; // Carta fittizia
                    const loopCheck = this.board.checkLoopHole(testCard, position, adjacentCards);

                    console.log(`[Game.findActiveLoops] Risultato checkLoopHole per ${position}:`, JSON.stringify(loopCheck));

                    if (loopCheck.isLoop) {
                        console.log(`[Game.findActiveLoops] TROVATO LOOP in ${position}: ${loopCheck.type} - ${loopCheck.description}`);
                        loops.push({
                            position: position,
                            type: loopCheck.type.toLowerCase(),
                            description: loopCheck.description
                        });
                    }
                } else if (adjacentCards.length > 0) {
                    console.log(`[Game.findActiveLoops] Posizione ${position} ha solo ${adjacentCards.length} carte adiacenti, non abbastanza per un loop.`);
                }
            }
        }

        console.log(`[Game.findActiveLoops] FINE ricerca. Trovati ${loops.length} loop attivi:`, JSON.stringify(loops));
        return loops;
    }

    /**
     * Verifica condizioni di vittoria per Ribaltone (ERA4) - CON LOG AGGIUNTIVI
     */
    checkReverserWinCondition(player, position, previousVertexControlStatus) {
        // --- NUOVO CONTROLLO ---
        // Verifica se la posizione è una cella valida per innescare ERA4 originale
        const validTriggerPositions = ["b1", "a2", "e1", "f2", "a5", "b6", "e6", "f5"];
        if (!validTriggerPositions.includes(position)) {
            this.logMessage(`[LOG ERA4 - Check Ignorato] La mossa in ${position} non è in una cella valida per triggerare ERA4 originale.`);
            return null; // Non può triggerare ERA4 da questa posizione
        }
        // --- FINE NUOVO CONTROLLO ---

        const adjacentVertices = Object.keys(previousVertexControlStatus);
        this.logMessage(`[LOG ERA4 - INIZIO CHECK] Controllo ribaltone per ${player.id} in ${position}. Vertici adiacenti: ${adjacentVertices.join(', ')}. Stato PRE: ${JSON.stringify(previousVertexControlStatus)}`);
        let reversedVertexId = null; // Memorizza l'ID del vertice ribaltato

        for (const vertexId of adjacentVertices) {
            const previousController = previousVertexControlStatus[vertexId];
            const currentController = this.board.vertexControl[vertexId]; // Stato DOPO il piazzamento
            const opponentColor = player.color === 'white' ? 'black' : 'white';
            this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Precedente: ${previousController || 'nessuno'}, Attuale: ${currentController || 'nessuno'}, Avversario: ${opponentColor}`);

            // Condizione 1: Il vertice ERA controllato dall'avversario
            const wasControlledByOpponent = previousController === opponentColor;
            this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Condizione 1 (Era controllato da avversario? ${opponentColor}): ${wasControlledByOpponent}`);
            // Condizione 2: Il vertice ORA è controllato dal giocatore (conquista avvenuta)
            const isNowControlledByPlayer = currentController === player.color;
            this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Condizione 2 (Ora controllato da giocatore? ${player.color}): ${isNowControlledByPlayer}`);

            // Modifica: Verifica se il vertice è stato effettivamente ribaltato (era dell'avversario, ora del giocatore)
            if (wasControlledByOpponent && isNowControlledByPlayer) {
                this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Vertice conquistato dall'avversario! Controllo celle adiacenti...`);
                const cellsAroundVertex = this.getCellsAdjacentToVertex(vertexId);
                this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Celle adiacenti da controllare: ${cellsAroundVertex.join(', ')}`);

                // Condizione 3: Tutte le celle occupate attorno al vertice sono controllate dal giocatore (o sono vuote)
                let allAdjacentCellsOk = true; // Assumiamo vero finché non troviamo una cella non OK
                for (const cellPos of cellsAroundVertex) {
                    const cardAtCell = this.board.getCardAt(cellPos);
                    const cellOwner = cardAtCell ? cardAtCell.ownerColor : 'vuota';
                    const cellOk = !cardAtCell || cellOwner === player.color;
                    this.logMessage(`[LOG ERA4 - Vertice ${vertexId} - Cella ${cellPos}] Contenuto: ${cardAtCell ? cardAtCell.toString() : 'Vuota'}, Owner: ${cellOwner}. Cella OK? ${cellOk}`);
                    if (!cellOk) {
                        allAdjacentCellsOk = false;
                        this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Cella ${cellPos} NON OK. Interrompo controllo celle per questo vertice.`);
                        break; // Inutile continuare a controllare le celle per questo vertice
                    }
                }
                this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Condizione 3 (Tutte celle adiacenti OK?): ${allAdjacentCellsOk}`);

                // Condizione 4: Almeno una cella adiacente è occupata (per evitare ribaltone "vuoto")
                const atLeastOneAdjacentOccupied = cellsAroundVertex.some(cellPos => this.board.isOccupied(cellPos));
                this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Condizione 4 (Almeno una cella adiacente occupata?): ${atLeastOneAdjacentOccupied}`);

                if (allAdjacentCellsOk && atLeastOneAdjacentOccupied) {
                    this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] CONDIZIONE RIBALTONE SODDISFATTA PER QUESTO VERTICE!`);
                    reversedVertexId = vertexId; // Memorizza l'ID del vertice ribaltato
                    break; // Trovato un ribaltone valido, inutile controllare altri vertici

                } else {
                     this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Condizioni celle adiacenti NON soddisfatte (allAdjacentCellsOk=${allAdjacentCellsOk}, atLeastOneAdjacentOccupied=${atLeastOneAdjacentOccupied}).`);
                }
            } else {
                 this.logMessage(`[LOG ERA4 - Vertice ${vertexId}] Condizioni di conquista/ribaltamento vertice NON soddisfatte.`);
            }
        } // Fine ciclo for sui vertici

        this.logMessage(`[LOG ERA4 - Fine Check] Ribaltone valido trovato per la mossa in ${position}? ${reversedVertexId ? `Sì, vertice ${reversedVertexId}` : 'No'}`);
        return reversedVertexId; // Restituisce l'ID del vertice ribaltato o null
    }


    /**
     * Verifica le condizioni per un Ribaltone (ERA4) come definito dall'utente.
     * @param {object} player Il giocatore che tenta il Ribaltone.
     * @param {Card} cardToPlace La carta piazzata per il Ribaltone.
     * @param {string} vertexPosition La posizione del vertice target (es. "a1").
     * @returns {boolean} True se le condizioni del Ribaltone sono soddisfatte.
     */
    checkTrueRibaltoneCondition(player, cardToPlace, vertexPosition) {
        this.logMessage(`[LOG RIBALTONE] Inizio verifica Ribaltone per ${player.id} su ${vertexPosition} con ${cardToPlace}`);
        const opponentColor = player.color === 'white' ? 'black' : 'white';

        // 1. Verifica se il vertice è effettivamente occupato dall'avversario
        const opponentCardOnVertex = this.board.getCardAt(vertexPosition);
        if (!opponentCardOnVertex || opponentCardOnVertex.ownerColor !== opponentColor) {
            this.logMessage(`[LOG RIBALTONE] Fallito: Vertice ${vertexPosition} non occupato da ${opponentColor}. Trovato: ${opponentCardOnVertex ? opponentCardOnVertex.ownerColor : 'null'}`);
            return false; // Non è un tentativo valido su vertice avversario
        }
        this.logMessage(`[LOG RIBALTONE] Vertice ${vertexPosition} occupato da ${opponentColor} (${opponentCardOnVertex}). OK.`);

        // 2. Verifica se la carta piazzata batte quella sul vertice
        if (!cardToPlace.winsAgainst(opponentCardOnVertex)) {
            this.logMessage(`[LOG RIBALTONE] Fallito: ${cardToPlace} non batte ${opponentCardOnVertex} sul vertice.`);
            return false;
        }
        this.logMessage(`[LOG RIBALTONE] ${cardToPlace} batte ${opponentCardOnVertex} sul vertice. OK.`);

        // 3. Verifica se la carta piazzata batte TUTTE le carte (indipendentemente dal proprietario) nelle CELLE adiacenti
        const adjacentCellPositions = this.getCellsAdjacentToVertex(this.getVertexIdForPosition(vertexPosition)); // Usa helper esistente
        this.logMessage(`[LOG RIBALTONE] Controllo celle adiacenti: ${adjacentCellPositions.join(', ')}`);
        for (const cellPos of adjacentCellPositions) {
            const adjacentCard = this.board.getCardAt(cellPos);
            if (adjacentCard) { // Se c'è una carta nella cella adiacente
                 this.logMessage(`[LOG RIBALTONE] Cella ${cellPos}: Trovata carta ${adjacentCard} (Owner: ${adjacentCard.ownerColor}). Controllo se ${cardToPlace} la batte...`);
                if (!cardToPlace.winsAgainst(adjacentCard)) {
                    // Se la carta piazzata NON batte ANCHE UNA SOLA delle carte adiacenti, il Ribaltone fallisce
                    this.logMessage(`[LOG RIBALTONE] Fallito: ${cardToPlace} NON batte ${adjacentCard} in cella adiacente ${cellPos}.`);
                    return false;
                }
                 this.logMessage(`[LOG RIBALTONE] Cella ${cellPos}: ${cardToPlace} batte ${adjacentCard}. OK.`);
            } else {
                 this.logMessage(`[LOG RIBALTONE] Cella ${cellPos}: Vuota. Ignorata.`);
            }
        }
        // Se il loop è terminato, significa che la carta piazzata batte tutte le carte presenti nelle celle adiacenti
        this.logMessage(`[LOG RIBALTONE] Tutte le condizioni (incluso battere TUTTE le carte adiacenti) soddisfatte! Ribaltone valido.`);
        return true; // Tutte le condizioni sono soddisfatte
    }

    /**
     * ✅ METODO DI PULIZIA CARTE DUPLICATE
     * Rimuove automaticamente dalle mani dei giocatori tutte le carte che sono già presenti sul board
     */
    cleanupDuplicateCards() {
        try {
            // Crea un Set con tutti gli ID delle carte presenti sul board
            const boardCardIds = new Set();
            for (const position in this.board.grid) {
                const card = this.board.grid[position];
                if (card && card.id) {
                    boardCardIds.add(card.id);
                }
            }
            
            console.log(`[CLEANUP DUPLICATES] Board contiene ${boardCardIds.size} carte: ${Array.from(boardCardIds).join(', ')}`);
            
            let totalDuplicatesRemoved = 0;
            
            // Per ogni giocatore, rimuovi le carte duplicate dalla mano
            for (const playerId in this.players) {
                const player = this.players[playerId];
                if (!player || !player.hand) continue;
                
                const originalHandSize = player.hand.length;
                const originalHand = [...player.hand];
                
                // Filtra la mano rimuovendo le carte che sono già sul board
                player.hand = player.hand.filter(card => {
                    if (card && card.id && boardCardIds.has(card.id)) {
                        console.log(`[CLEANUP DUPLICATES] Rimossa carta duplicata ${card.id} dalla mano di ${playerId} (${player.color})`);
                        totalDuplicatesRemoved++;
                        return false; // Rimuovi la carta
                    }
                    return true; // Mantieni la carta
                });
                
                if (player.hand.length !== originalHandSize) {
                    console.log(`[CLEANUP DUPLICATES] Player ${playerId} (${player.color}): mano ridotta da ${originalHandSize} a ${player.hand.length} carte`);
                    console.log(`[CLEANUP DUPLICATES] Carte originali: ${originalHand.map(c => c.id).join(', ')}`);
                    console.log(`[CLEANUP DUPLICATES] Carte rimaste: ${player.hand.map(c => c.id).join(', ')}`);
                }
            }
            
            if (totalDuplicatesRemoved > 0) {
                console.log(`[CLEANUP DUPLICATES] ⚠️  PULIZIA COMPLETATA: Rimosse ${totalDuplicatesRemoved} carte duplicate dalle mani`);
                this.logMessage(`Sistema di pulizia automatica: rimosse ${totalDuplicatesRemoved} carte duplicate dalle mani dei giocatori`);
            } else {
                console.log(`[CLEANUP DUPLICATES] ✅ Nessuna carta duplicata trovata - stato pulito`);
            }
            
        } catch (error) {
            console.error(`[CLEANUP DUPLICATES] Errore durante la pulizia carte duplicate:`, error);
        }
    }

}

module.exports = Game;
module.exports.INITIAL_HAND_SIZE = INITIAL_HAND_SIZE;
module.exports.MAX_HAND_SIZE = MAX_HAND_SIZE;
module.exports.RATING_LEVELS = RATING_LEVELS;
module.exports.INITIAL_RATING = INITIAL_RATING;

// Rimosso blocco checkStallCondition da fuori classe



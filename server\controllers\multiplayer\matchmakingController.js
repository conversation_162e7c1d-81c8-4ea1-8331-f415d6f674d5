/**
 * Matchmaking Controller
 * Handles matchmaking queue and player matching
 */

const MatchmakingQueue = require('../../models/multiplayer/MatchmakingQueue');
const gameController = require('./gameController');
const User = require('../../database/models/User');

// In-memory queue for faster matchmaking
const waitingPlayers = [];

class MatchmakingController {
    /**
     * Add a player to the matchmaking queue
     * @param {Object} playerInfo - Player info
     * @returns {Promise<Object>} - Queue entry
     */
    async addToQueue(playerInfo) {
        try {
            const { socketId, userId, username, rating = 1000 } = playerInfo;

            // Check if player is already in queue
            const existingPlayer = waitingPlayers.find(p => p.userId === userId);
            if (existingPlayer) {
                console.log(`Player ${username} (${userId}) already in queue, updating socket ID`);
                existingPlayer.socketId = socketId;
                return existingPlayer;
            }

            // Add to database queue
            await MatchmakingQueue.addToQueue({
                userId,
                rating
            });

            // Add to in-memory queue
            waitingPlayers.push({
                socketId,
                userId,
                username,
                rating,
                joinTime: Date.now()
            });

            console.log(`Player ${username} (${userId}) added to matchmaking queue`);
            console.log(`Current waiting players: ${waitingPlayers.length}`, waitingPlayers.map(p => ({ username: p.username, userId: p.userId })));

            return {
                userId,
                username,
                rating,
                joinTime: new Date()
            };
        } catch (error) {
            console.error('Error adding player to matchmaking queue:', error);
            throw error;
        }
    }

    /**
     * Remove a player from the matchmaking queue
     * @param {string} socketId - Socket ID
     * @returns {Promise<boolean>} - Success status
     */
    async removeFromQueue(socketId) {
        try {
            // Find player in in-memory queue
            const playerIndex = waitingPlayers.findIndex(p => p.socketId === socketId);

            if (playerIndex === -1) {
                return false;
            }

            const player = waitingPlayers[playerIndex];

            // Remove from database queue
            await MatchmakingQueue.removeFromQueue(player.userId);

            // Remove from in-memory queue
            waitingPlayers.splice(playerIndex, 1);

            console.log(`Player ${player.username} (${player.userId}) removed from matchmaking queue`);

            return true;
        } catch (error) {
            console.error('Error removing player from matchmaking queue:', error);
            throw error;
        }
    }

    /**
     * Find matches for all waiting players
     * @returns {Promise<Array>} - Array of matches
     */
    async findMatches() {
        try {
            // Log solo quando ci sono giocatori in attesa
            if (waitingPlayers.length > 0) {
                console.log(`[MATCHMAKING] Finding matches. Waiting players: ${waitingPlayers.length}`);
            }
            const matches = [];

            // Sort players by wait time (oldest first)
            waitingPlayers.sort((a, b) => a.joinTime - b.joinTime);

            // Process players
            while (waitingPlayers.length >= 2) {
                const player1 = waitingPlayers[0];

                // Find best match for player1
                let bestMatchIndex = -1;
                let bestMatchScore = Infinity;

                for (let i = 1; i < waitingPlayers.length; i++) {
                    const player2 = waitingPlayers[i];

                    // Calculate match score (lower is better)
                    // Based on rating difference and wait time
                    const ratingDiff = Math.abs(player1.rating - player2.rating);
                    const waitTimeDiff = Math.abs(player1.joinTime - player2.joinTime);

                    // Normalize factors
                    const normalizedRatingDiff = ratingDiff / 400; // Max reasonable difference
                    const normalizedWaitTime = waitTimeDiff / (5 * 60 * 1000); // 5 minutes

                    // Calculate score (70% rating, 30% wait time)
                    const score = (normalizedRatingDiff * 0.7) + (normalizedWaitTime * 0.3);

                    // Update best match if better
                    if (score < bestMatchScore) {
                        bestMatchScore = score;
                        bestMatchIndex = i;
                    }
                }

                // If no good match found, break
                if (bestMatchIndex === -1 || bestMatchScore > 0.8) {
                    break;
                }

                // Create match
                const player2 = waitingPlayers[bestMatchIndex];

                // Nota: NON creiamo la partita qui. La creazione è delegata a createAndStartGame in server.js
                // per evitare duplicazione di partite
                const gameInfo = { gameId: 'pending' }; // ID temporaneo, sarà sostituito dalla vera partita

                // Add match to results
                matches.push({
                    gameId: gameInfo.gameId,
                    players: [
                        {
                            socketId: player1.socketId,
                            userId: player1.userId,
                            username: player1.username,
                            rating: player1.rating
                        },
                        {
                            socketId: player2.socketId,
                            userId: player2.userId,
                            username: player2.username,
                            rating: player2.rating
                        }
                    ]
                });

                console.log(`[MATCHMAKING] Match found: ${player1.username} vs ${player2.username}`);
                console.log(`[MATCHMAKING] Remaining waiting players: ${waitingPlayers.length - 2}`);

                // Remove matched players from queue
                waitingPlayers.splice(bestMatchIndex, 1);
                waitingPlayers.splice(0, 1);

                // Remove from database queue
                await MatchmakingQueue.updatePlayerStatus(player1.userId, 'matched');
                await MatchmakingQueue.updatePlayerStatus(player2.userId, 'matched');
            }

            return matches;
        } catch (error) {
            console.error('Error finding matches:', error);
            throw error;
        }
    }

    /**
     * Find a match for a specific player
     * @param {Object} playerInfo - Player info
     * @returns {Promise<Object|null>} - Match info or null if no match found
     */
    async findMatchForPlayer(playerInfo) {
        try {
            const { socketId, userId, username, rating = 1000 } = playerInfo;

            // Debug log for matchmaking
                    // Ricerca match per giocatore (${username})

            // Check if player is already in queue
            const existingIndex = waitingPlayers.findIndex(p => p.socketId === socketId);
            if (existingIndex !== -1) {
                // Remove existing entry
                waitingPlayers.splice(existingIndex, 1);
            }

            // Find best match
            let bestMatchIndex = -1;
            let bestMatchScore = Infinity;

            for (let i = 0; i < waitingPlayers.length; i++) {
                const potentialMatch = waitingPlayers[i];

                // Calculate match score (lower is better)
                const ratingDiff = Math.abs(rating - potentialMatch.rating);
                const waitTime = Date.now() - potentialMatch.joinTime;
                const waitTimeSeconds = waitTime / 1000;

                // Definisci il range di rating accettabile basato sul tempo di attesa
                // Inizia con ±50 punti e gradualmente espandi fino a ±400 dopo 60 secondi
                let acceptableRatingRange;
                if (waitTimeSeconds < 15) {
                    // Nei primi 15 secondi, range molto stretto (±50)
                    acceptableRatingRange = 50;
                } else if (waitTimeSeconds < 30) {
                    // Da 15 a 30 secondi, range più ampio (±100)
                    acceptableRatingRange = 100;
                } else if (waitTimeSeconds < 60) {
                    // Da 30 a 60 secondi, range ancora più ampio (±200)
                    acceptableRatingRange = 200;
                } else {
                    // Dopo 60 secondi, range molto ampio (±400)
                    acceptableRatingRange = 400;
                }
                
                // Valutazione match rating

                // Se la differenza di rating è maggiore del range accettabile, salta questo match
                if (ratingDiff > acceptableRatingRange) {
                    // Differenza rating troppo grande, salta questo match
                    continue;
                }

                // Normalize factors per il calcolo del punteggio
                const normalizedRatingDiff = ratingDiff / acceptableRatingRange; // Normalizza in base al range corrente
                const normalizedWaitTime = Math.min(waitTime / (5 * 60 * 1000), 1); // 5 minuti max

                // Calculate score (70% rating, 30% wait time)
                const score = (normalizedRatingDiff * 0.7) - (normalizedWaitTime * 0.3);

                // Update best match if better
                if (score < bestMatchScore) {
                    bestMatchScore = score;
                    bestMatchIndex = i;
                }
            }

            // If good match found
            if (bestMatchIndex !== -1) {
                const match = waitingPlayers[bestMatchIndex];

                console.log(`Match found: ${username} vs ${match.username}`);

                // Create game
                const gameInfo = gameController.createGame({ isPrivate: false });

                // Add players to game
                gameController.addPlayerToGame(gameInfo.gameId, match.socketId, match.username, match.rating);
                gameController.addPlayerToGame(gameInfo.gameId, socketId, username, rating);

                // Start the game
                const startResult = gameController.startGame(gameInfo.gameId);
                if (!startResult.success) {
                    console.error(`Failed to start game ${gameInfo.gameId}: ${startResult.reason}`);
                }

                // Remove matched player from queue
                waitingPlayers.splice(bestMatchIndex, 1);

                // Remove from database queue
                await MatchmakingQueue.updatePlayerStatus(match.userId, 'matched');

                console.log(`Match created: ${username} vs ${match.username} in game ${gameInfo.gameId}`);

                return {
                    gameId: gameInfo.gameId,
                    opponent: {
                        socketId: match.socketId,
                        userId: match.userId,
                        name: match.username,
                        rating: match.rating
                    }
                };
            }

            // No match found, add player to queue

            waitingPlayers.push({
                socketId,
                userId,
                username,
                rating,
                joinTime: Date.now()
            });

            // Add to database queue
            await MatchmakingQueue.addToQueue({
                userId,
                rating
            });

            console.log(`Player ${username} added to matchmaking queue (total: ${waitingPlayers.length})`);

            return null;
        } catch (error) {
            console.error('Error finding match for player:', error);
            throw error;
        }
    }

    /**
     * Get all players in the matchmaking queue
     * @returns {Promise<Array>} - Array of players
     */
    async getQueuedPlayers() {
        try {
            // Get from database for complete info
            const players = await MatchmakingQueue.getAllInQueue();

            return players;
        } catch (error) {
            console.error('Error getting queued players:', error);
            throw error;
        }
    }

    /**
     * Clean up the matchmaking queue
     * @returns {Promise<number>} - Number of entries removed
     */
    async cleanupQueue() {
        try {
            // Clean up database queue
            const dbRemoved = await MatchmakingQueue.cleanupOldEntries();

            // Clean up in-memory queue
            const now = Date.now();
            const oldThreshold = now - (30 * 60 * 1000); // 30 minutes

            const initialLength = waitingPlayers.length;

            // Remove old entries
            for (let i = waitingPlayers.length - 1; i >= 0; i--) {
                if (waitingPlayers[i].joinTime < oldThreshold) {
                    waitingPlayers.splice(i, 1);
                }
            }

            const memoryRemoved = initialLength - waitingPlayers.length;

            console.log(`Cleaned up matchmaking queue: ${dbRemoved} DB entries, ${memoryRemoved} memory entries`);

            return dbRemoved;
        } catch (error) {
            console.error('Error cleaning up matchmaking queue:', error);
            throw error;
        }
    }
}

module.exports = new MatchmakingController();

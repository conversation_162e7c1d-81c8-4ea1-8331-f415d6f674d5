token-manager.js:173 [TOKEN MANAGER] Token Manager inizializzato
game:481 [STATE] Controllo iniziale:
game:482 [STATE] - skipAnimations: false
game:483 [STATE] - fullInterface: false
game:484 [STATE] - fromSessionStorage: false
game:485 [STATE] - savedState: null
game:491 [STATE] - shouldShowFullInterface: null
game:764 [STATE] Nessuna interfaccia completa richiesta, pulizia stato
game:439 [STATE] Pulizia stato
script.js:2949 [DRAG FIX] Removed setup-animation element from DOM
script.js:1380 [SOCKET INIT] Creating socket with token: Present
script.js:1358 [SOCKET HANDLERS] Handler socket aggiuntivi inizializzati
local-game.js:198 [LOCAL GAME] Manager caricato e pronto
online-game.js:343 [ONLINE GAME] Manager caricato e pronto
game-mode-manager.js:218 [GAME MODE] Manager principale caricato e pronto
player-names-protection.js:325 [NAMES PROTECTION] Sistema di protezione nomi caricato
smooth-loading.js:89 [SMOOTH LOADING] Inizializzazione sistema loading fluido
smooth-loading.js:205 [SMOOTH LOADING] Sistema di loading fluido caricato
script.js:2854 [PRELOAD] Avvio precaricamento immagini delle carte: Array(4)
script.js:3332 [PLAYER NAMES] Modalità locale - Nome del giocatore 1 impostato a: giggio
script.js:15072 [INIT] Pagina di gioco rilevata, evito fullResetGameUI per prevenire refresh continui
script.js:16831 [PSN] Inizializzazione sistema PSN...
multiplayer.js:1772 [MULTIPLAYER] DOMContentLoaded - Inizio inizializzazione
multiplayer.js:1773 [MULTIPLAYER] URL: http://localhost:3000/game
multiplayer.js:234 [CHAT] DEBUG: Chat event listener sarà registrato nell'evento connect quando socket è connesso
multiplayer.js:235 [CHAT] DEBUG: Socket ID in initSocketEvents: undefined (potrebbe essere undefined)
multiplayer.js:1977 [CHAT] DEBUG: Socket presente ma potrebbe non essere ancora connesso. Socket ID: undefined
multiplayer.js:1978 [CHAT] Event listener per chatMessage sarà registrato quando il socket è connesso
multiplayer.js:1704 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
multiplayer.js:1725 [HAND SLOTS] Creati slot per la mano del giocatore 1
multiplayer.js:1745 [HAND SLOTS] Creati slot per la mano del giocatore 2
multiplayer.js:1926 [ANIMATION] Osservatore impostato per rilevare il completamento dell'animazione
online-play-enhancer.js:86 [ONLINE ENHANCER] DOM pronto, inizializzo enhancer...
victory-fix.js:158 victory-fix.js caricato.
script.js:2859 [PRELOAD] SUCCESSO precaricamento 1/4: http://localhost:3000/img/carte/card-back.webp
script.js:2859 [PRELOAD] SUCCESSO precaricamento 4/4: http://localhost:3000/img/Cover carte/cover.png
script.js:2859 [PRELOAD] SUCCESSO precaricamento 2/4: http://localhost:3000/img/carte/card-back.png
script.js:2859 [PRELOAD] SUCCESSO precaricamento 3/4: http://localhost:3000/img/Cover carte/cover.webp
script.js:1758 [SOCKET CONNECT] myPlayerId attuale: GbGVf0be47MwQ8GGAAAO socket.id: GbGVf0be47MwQ8GGAAAO
script.js:1762 [CHAT] DEBUG: Registrando event listener per chatMessage su socket connesso. Socket ID: GbGVf0be47MwQ8GGAAAO
script.js:1777 [CHAT] Event listener per chatMessage registrato su socket connesso
script.js:1781 [CHAT] DEBUG: Numero di listener per chatMessage: 1
inspector.b9415ea5.js:12 Could not access stylesheet rules: SecurityError: Failed to read the 'cssRules' property from 'CSSStyleSheet': Cannot access rules
    at inspector.b9415ea5.js:12:57303
    at Array.forEach (<anonymous>)
    at n (inspector.b9415ea5.js:12:57152)
    at g (inspector.b9415ea5.js:12:55110)
    at 1MLsW.@reduxjs/toolkit (inspector.b9415ea5.js:12:1920)
    at u (inspector.b9415ea5.js:1:705)
    at d (inspector.b9415ea5.js:1:809)
    at fLzap../distance (inspector.b9415ea5.js:1:153421)
    at u (inspector.b9415ea5.js:1:705)
    at d (inspector.b9415ea5.js:1:809)
(anonime) @ inspector.b9415ea5.js:12
ads.914af30a.js:1 Ads initialization already in progress or completed
ads.914af30a.js:1 Ads initialization already in progress or completed
ads.914af30a.js:1 Ads initialization already in progress or completed
inspector.b9415ea5.js:1 Error: Minified React error #31; visit https://reactjs.org/docs/error-decoder.html?invariant=31&args[]=%5Bobject%20Promise%5D for the full message or use the non-minified dev environment for full errors and additional helpful warnings.
    at n0 (inspector.b9415ea5.js:1:65377)
    at l (inspector.b9415ea5.js:1:70370)
    at ol (inspector.b9415ea5.js:1:82233)
    at i (inspector.b9415ea5.js:1:130901)
    at lM (inspector.b9415ea5.js:1:110198)
    at inspector.b9415ea5.js:1:110064
    at lO (inspector.b9415ea5.js:1:110072)
    at lC (inspector.b9415ea5.js:1:106823)
    at lw (inspector.b9415ea5.js:1:105378)
    at C (inspector.b9415ea5.js:1:141367)
s7 @ inspector.b9415ea5.js:1
inspector.b9415ea5.js:1 Uncaught Error: Minified React error #31; visit https://reactjs.org/docs/error-decoder.html?invariant=31&args[]=%5Bobject%20Promise%5D for the full message or use the non-minified dev environment for full errors and additional helpful warnings.
    at n0 (inspector.b9415ea5.js:1:65377)
    at l (inspector.b9415ea5.js:1:70370)
    at ol (inspector.b9415ea5.js:1:82233)
    at i (inspector.b9415ea5.js:1:130901)
    at lM (inspector.b9415ea5.js:1:110198)
    at inspector.b9415ea5.js:1:110064
    at lO (inspector.b9415ea5.js:1:110072)
    at lC (inspector.b9415ea5.js:1:106823)
    at lw (inspector.b9415ea5.js:1:105378)
    at C (inspector.b9415ea5.js:1:141367)
smooth-loading.js:17 [SMOOTH LOADING] Avvio animazioni di caricamento
smooth-loading.js:58 [SMOOTH LOADING] Animazioni di caricamento completate
ads.914af30a.js:1 Attempting to initialize AdUnit
ads.914af30a.js:1 AdUnit initialized successfully
ads.914af30a.js:1 Ads initialized successfully for: http://localhost:3000/game
ads.914af30a.js:1 Ads initialization already in progress or completed
online-play-enhancer.js:200 [ONLINE ENHANCER] Finestra completamente caricata
online-play-enhancer.js:208 [ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili al caricamento
script.js:90 [PERSISTENCE] Stato salvato scaduto o non presente
script.js:158 [PERSISTENCE] Stato salvato rimosso
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata

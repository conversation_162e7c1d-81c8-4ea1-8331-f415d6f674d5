/**
 * Multiplayer styles for Skèmino
 */

/* Prevenzione flash nero durante animazioni setup */
#setup-animation,
#game-container {
    /* Assicura uno sfondo continuo durante le transizioni */
    background: linear-gradient(135deg, rgba(23, 42, 69, 0.95), rgba(15, 30, 48, 0.95));
    transition: opacity 0.6s ease-in-out;
}

/* Durante le transizioni di setup, mantieni sempre uno sfondo visibile */
body:has(#setup-animation[style*="opacity: 0"]) #game-container,
body.setup-transitioning #game-container {
    background: linear-gradient(135deg, rgba(23, 42, 69, 0.95), rgba(15, 30, 48, 0.95));
}

/* Assicura che il body non mostri mai sfondo nero durante le animazioni multiplayer */
body {
    background: linear-gradient(135deg, #172a45 0%, #0f1b2d 90%) !important;
}

/* Regole specifiche per l'animazione dei nomi dei giocatori */
#dynamic-names-animation {
    /* Assicura uno sfondo fluido durante l'animazione dei nomi */
    background: linear-gradient(135deg, rgba(23, 42, 69, 0.95), rgba(15, 30, 48, 0.95)) !important;
    transition: opacity 0.4s ease-out !important;
}

/* Matchmaking Modal */
.matchmaking-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1100;
}

.matchmaking-content {
    background: linear-gradient(135deg, #172a45 0%, #0f1b2d 90%);
    border-radius: 15px;
    padding: 25px;
    width: 90%;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5), 0 0 40px rgba(0, 100, 255, 0.2);
    border: 2px solid rgba(100, 150, 255, 0.25);
    color: white;
}

.matchmaking-content h2 {
    margin-top: 0;
    color: #e0f0ff;
    font-size: 1.5em;
    text-shadow: 0 0 10px rgba(0, 120, 255, 0.6);
}

.matchmaking-status {
    margin: 20px 0;
    font-size: 16px;
    color: rgba(220, 230, 255, 0.9);
    line-height: 1.5;
}

.matchmaking-spinner {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 40, 100, 0.2);
    border-radius: 50%;
    border-top-color: #3498db;
    border-left-color: #3498db;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
    box-shadow: 0 0 25px rgba(50, 150, 255, 0.3);
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.matchmaking-buttons {
    margin-top: 30px;
}

.cancel-matchmaking {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 14px 30px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    position: relative;
    overflow: hidden;
    z-index: 1;
    display: inline-block;
}

.cancel-matchmaking:hover {
    background: linear-gradient(135deg, #e74c3c, #d73121);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
}

.cancel-matchmaking:active {
    transform: translateY(1px);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.cancel-matchmaking::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    z-index: -1;
}

/* Private Game Controls */
.private-game-controls {
    margin: 20px 0;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.private-game-controls h3 {
    margin-top: 0;
    color: #333;
}

.create-game-section, .join-game-section {
    margin-bottom: 15px;
}

.invite-code-display {
    margin: 10px 0;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: monospace;
    font-size: 18px;
    letter-spacing: 2px;
}

.invite-code-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    width: 150px;
    text-transform: uppercase;
}

.turn-timeout-input {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    width: 60px;
    text-align: center;
}

.create-game-btn, .join-game-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.create-game-btn:hover, .join-game-btn:hover {
    background-color: #2980b9;
}

.create-game-btn:disabled, .join-game-btn:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

/* Turn Timer */
.turn-timer-container {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 5px 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

.turn-timer {
    font-family: monospace;
    font-size: 20px;
    font-weight: bold;
    color: #333;
}

.turn-timer.warning {
    color: #e74c3c;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Notifications */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    z-index: 1000;
}

.notification {
    background-color: #3498db;
    color: white;
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transform: translateX(120%);
    transition: transform 0.3s ease-out;
}

.notification.show {
    transform: translateX(0);
}

.notification.error-notification {
    background-color: #e74c3c;
    padding: 15px;
}

.notification .reload-btn {
    background-color: #fff;
    color: #e74c3c;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
    display: block;
    width: 100%;
    transition: background-color 0.3s;
}

.notification .reload-btn:hover {
    background-color: #f8f8f8;
}

/* Level Up Modal */
.level-up-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    transition: opacity 0.3s;
}

.level-up-modal.show {
    opacity: 1;
}

.level-up-content {
    background-color: #fff;
    border-radius: 8px;
    padding: 30px;
    width: 90%;
    max-width: 500px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: transform 0.3s;
}

.level-up-modal.show .level-up-content {
    transform: scale(1);
}

.level-up-content h2 {
    margin-top: 0;
    color: #f39c12;
    font-size: 28px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.level-change {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 30px 0;
}

.old-level, .new-level {
    text-align: center;
}

.old-level img, .new-level img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 3px solid #ddd;
    padding: 5px;
    background-color: #f9f9f9;
}

.new-level img {
    border-color: #f39c12;
    animation: glow 2s infinite;
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(243, 156, 18, 0.5); }
    50% { box-shadow: 0 0 20px rgba(243, 156, 18, 0.8); }
    100% { box-shadow: 0 0 5px rgba(243, 156, 18, 0.5); }
}

.level-arrow {
    font-size: 40px;
    margin: 0 20px;
    color: #f39c12;
}

.close-btn {
    background-color: #f39c12;
    color: white;
    border: none;
    padding: 10px 25px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 18px;
    transition: background-color 0.3s;
    margin-top: 20px;
}

.close-btn:hover {
    background-color: #e67e22;
}

/* Login Prompt Modal */
.login-prompt-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s;
}

.login-prompt-modal.show {
    opacity: 1;
}

.login-prompt-content {
    background-color: #fff;
    border-radius: 8px;
    padding: 25px;
    width: 90%;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.login-prompt-content h2 {
    margin-top: 0;
    color: #333;
}

.login-prompt-content p {
    margin-bottom: 20px;
    color: #555;
}

.login-prompt-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.login-btn, .register-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.login-btn:hover, .register-btn:hover {
    background-color: #2980b9;
}

.cancel-btn {
    background-color: #95a5a6;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s;
}

.cancel-btn:hover {
    background-color: #7f8c8d;
}

/* Responsive Styles */
/* Chat area */
.chat-area {
    padding-top: 8px;
    position: absolute;
    bottom: 15px; /* Coerente con gap delle altre aree sidebar */
    left: 12px; /* Coerente con padding board-sidebar */
    right: 12px;
    background: linear-gradient(135deg, rgba(20, 40, 80, 0.4), rgba(10, 20, 40, 0.4)); /* Coerente con board-sidebar */
    border-radius: 10px; /* Coerente con altre aree sidebar */
    padding: 8px; /* Padding coerente */
    box-shadow: inset 0 0 30px rgba(0,0,0,0.4), 0 5px 15px rgba(0,0,0,0.3); /* Coerente con board-sidebar */
    border: 1px solid rgba(50, 100, 200, 0.3); /* Coerente con board-sidebar */
    backdrop-filter: blur(5px); /* Aggiunto per coerenza */
}

.chat-header {
    font-size: 13px; /* Leggermente più grande per coerenza */
    font-weight: bold;
    color: #7f8c8d;
    margin-bottom: 6px; /* Spaziatura coerente */
    border-bottom: 1px dotted #ddd;
    padding-bottom: 4px;
}

.chat-header i {
    margin-right: 5px;
    color: #3498db;
}

/* Stile per la chat quando non è in multiplayer */
.chat-area:not(.multiplayer-active) .chat-header {
    color: #95a5a6;
}

.chat-area:not(.multiplayer-active)::before {
    content: "Disponibile solo in modalità multiplayer";
    display: block;
    text-align: center;
    font-style: italic;
    color: #95a5a6;
    margin: 5px 0;
    font-size: 11px;
}

.chat-area:not(.multiplayer-active) .chat-input-container,
.chat-area:not(.multiplayer-active) .emoji-picker {
    display: none;
}

.chat-area:not(.multiplayer-active) .chat-messages {
    min-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(236, 240, 241, 0.5);
    margin-bottom: 0;
}

.chat-area.multiplayer-active .chat-messages {
    display: block;
}

.chat-messages {
    height: 75px !important; /* Altezza ulteriormente ridotta per creare più spazio superiore */
    max-height: 75px !important; /* Altezza massima fissa */
    min-height: 75px !important; /* Altezza minima uguale per evitare ridimensionamenti */
    overflow-y: auto;
    overflow-x: hidden;
    margin-bottom: 10px; /* Spaziatura coerente con design sidebar */
    padding: 5px;
    border-radius: 6px; /* Border radius coerente con design generale */
    background-color: rgba(245, 245, 245, 0.9);
    font-size: 13px;
    border: 1px solid rgba(189, 195, 199, 0.5);
    scroll-behavior: smooth; /* Scroll fluido */
}

/* CSS Fluid per responsive design avanzato */
@media (min-width: 992px) {
    .chat-messages {
        height: clamp(65px, 7vh, 80px) !important; /* Altezza fluida ridotta con limiti */
        max-height: clamp(65px, 7vh, 80px) !important;
        min-height: clamp(55px, 5vh, 70px) !important;
    }
}

@media (min-width: 1200px) {
    .chat-messages {
        height: clamp(70px, 8vh, 85px) !important; /* Leggero aumento per schermi grandi ma ridotto */
        max-height: clamp(70px, 8vh, 85px) !important;
        min-height: clamp(60px, 6vh, 75px) !important;
    }
}

/* Stili personalizzati per la scrollbar della chat */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: rgba(189, 195, 199, 0.2);
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: rgba(52, 152, 219, 0.6);
    border-radius: 3px;
    transition: background 0.2s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 152, 219, 0.8);
}

.chat-message {
    margin-bottom: 4px;
    padding: 6px;
    border-radius: 4px;
    word-wrap: break-word;
    font-size: 12px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;
    animation: fadeIn 0.3s ease-out;
    clear: both;
    max-width: 95%;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

.chat-message.highlight {
    animation: highlight 1s ease;
}

@keyframes highlight {
    0% { background-color: rgba(255, 255, 0, 0.3); }
    100% { background-color: inherit; }
}

.chat-message.self {
    background-color: rgba(52, 152, 219, 0.2);
    text-align: right;
    border-left: 3px solid #3498db;
    margin-left: auto;
    color: #000; /* Testo nero */
}

.chat-message.other {
    background-color: rgba(46, 204, 113, 0.2);
    text-align: left;
    border-left: 3px solid #27ae60;
    margin-right: auto;
    color: #000; /* Testo nero */
    font-weight: bold; /* Grassetto per maggiore visibilità */
}

.chat-message.system {
    background-color: rgba(241, 196, 15, 0.1);
    text-align: center;
    font-style: italic;
    color: #7f8c8d;
    padding: 4px;
    margin: 4px auto;
    border-top: 1px dashed #ddd;
    border-bottom: 1px dashed #ddd;
    font-size: 11px;
    max-width: 100%;
    box-shadow: none;
    border-left: none;
}

.chat-input-container {
    display: flex;
    position: relative;
}

#chat-input {
    flex: 1;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px 8px;
    font-size: 12px;
    font-family: inherit;
    height: 26px;
}

.emoji-button, .send-button {
    background-color: transparent;
    border: none;
    color: #7f8c8d;
    font-size: 14px;
    padding: 0 6px;
    cursor: pointer;
    transition: color 0.2s;
    display: flex;
    align-items: center;
}

.send-button {
    color: #3498db;
}

.emoji-button:hover, .send-button:hover {
    color: #2980b9;
}

.emoji-picker {
    display: none;
    position: absolute;
    bottom: 35px;
    right: 0;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    width: 180px;
    padding: 8px;
    z-index: 100;
}

.emoji-picker.active {
    display: block;
}

.emoji-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.emoji-item {
    font-size: 16px;
    padding: 4px;
    cursor: pointer;
    transition: transform 0.1s, background-color 0.1s;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    width: 28px;
    height: 28px;
}

.emoji-item:hover {
    background-color: #f0f0f0;
    transform: scale(1.2);
}

.sender-name {
    font-weight: bold;
    font-size: 12px;
    display: inline;
    margin-right: 3px;
}

.message-text {
    display: inline;
    word-break: break-word;
}

/* Specifiche modifiche per l'interfaccia "Gioca Online" */
/* Questa classe verrà applicata automaticamente al container quando si avvia dal pulsante "Gioca Online" */
/* Regola rimossa - gestita da online-interface.css */

/* Hand area - usa layout CSS nativo */

/* Carta dimensioni - rimosso per usare variabili CSS native */

/* Miglioramento visivo delle aree dei giocatori */
body #game-container.online-play-interface #player1-area,
body #game-container.online-play-interface #player2-area {
    padding: 15px !important;
    border-radius: 15px !important;
    margin-bottom: 15px !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* Miglioramento visivo dell'area mano */
body #game-container.online-play-interface .hand-area {
    border-radius: 12px !important;
    padding: 15px !important;
    box-shadow: inset 0 0 20px rgba(0, 20, 50, 0.5) !important;
}

@media (max-width: 768px) {
    .level-change {
        flex-direction: column;
    }

    .level-arrow {
        transform: rotate(90deg);
        margin: 15px 0;
    }

    .login-prompt-buttons {
        flex-direction: column;
    }

    .login-btn, .register-btn, .cancel-btn {
        margin-bottom: 10px;
    }
    
    .chat-area {
        margin-top: 10px;
    }
    
    .chat-messages {
        height: 10vh !important; /* Altezza fluida ridotta basata su viewport per mobile */
        max-height: 10vh !important;
        min-height: 8vh !important;
    }
    
    .emoji-picker {
        width: 180px;
    }
    
    .emoji-item {
        font-size: 18px;
        padding: 3px;
    }
    
    /* Responsive per l'interfaccia "Gioca Online" su mobile */
    body #game-container.online-play-interface #players-column {
        width: 100% !important;
    }
    
    /* Hand area responsive - usa layout CSS nativo */
    
    /* Carte responsive rimosso - usa dimensioni CSS native */
    
    body #game-container.online-play-interface #player1-area,
    body #game-container.online-play-interface #player2-area {
        padding: 10px !important;
        margin-bottom: 10px !important;
    }
}
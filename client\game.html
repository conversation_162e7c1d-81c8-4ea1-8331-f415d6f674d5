<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Skèmino - Partita Multiplayer Online">
    <meta name="theme-color" content="#172a45">
    <title>Skèmino - Partita Online</title>
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="stylesheet" href="css/critical-layout.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/multiplayer.css">
    <link rel="stylesheet" href="css/card-animations.css">
    <link rel="stylesheet" href="css/psn-unified.css">
    <link rel="stylesheet" href="css/board-snapshot.css">
    <link rel="stylesheet" href="css/index.css">
    <link rel="stylesheet" href="css/page-transitions.css">
    <link rel="stylesheet" href="css/dice-animation.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Precaricamento immagini critiche -->
    <link rel="preload" href="img/carte/card-back.webp" as="image" type="image/webp" fetchpriority="high">
    <link rel="preload" href="img/carte/skemino.webp" as="image" type="image/webp" fetchpriority="high">
</head>
<body>
    <!-- Precarica le immagini di copertura delle carte -->
    <div class="preload-card-backs" aria-hidden="true">
        <img src="img/carte/card-back.webp" alt="Card Back" class="card-back-image"
             loading="eager" fetchpriority="high" decoding="sync">
        <img src="/img/Cover carte/cover.png" alt="Card Back" class="card-back-image"
             loading="eager" fetchpriority="high" decoding="sync">
    </div>

    <!-- Overlay scuro per l'animazione dei dadi -->
    <div id="dice-animation-overlay" class="dice-animation-overlay" data-hidden="true"></div>
    
    <!-- Area animazione dadi (mostrata durante la preparazione della partita) -->
    <div id="dice-animation-area" class="dice-animation-area" data-hidden="true">
        <div class="dice-animation-container">
            <h3>Preparazione Partita...</h3>
            <div id="dice-area" class="dice-area">
                <!-- I dadi appariranno qui -->
            </div>
            <p id="dice-result-text" class="dice-result-text">Lancio dei dadi per determinare chi inizia...</p>
        </div>
    </div>
    

    <!-- Container principale del gioco -->
    <div id="game-container" style="display: none;">
        <!-- Menu laterale compresso per l'interfaccia di gioco -->
        <div id="game-sidebar" class="game-sidebar">
            <div class="logo">
                <img src="img/carte/skemino.webp" alt="Skèmino" class="logo-image">
            </div>
            <nav>
                <ul>
                    <li class="active"><i class="fas fa-play"></i><span>Gioca</span><div class="menu-tooltip">Gioca</div></li>
                    <li><i class="fas fa-book"></i><span>Regole</span><div class="menu-tooltip">Regole</div></li>
                    <li><i class="fas fa-trophy"></i><span>Tornei</span><div class="menu-tooltip">Tornei</div></li>
                    <li><i class="fas fa-graduation-cap"></i><span>Tutorial</span><div class="menu-tooltip">Tutorial</div></li>
                    <li><i class="fas fa-chart-line"></i><span>Statistiche</span><div class="menu-tooltip">Statistiche</div></li>
                    <li><i class="fas fa-users"></i><span>Community</span><div class="menu-tooltip">Community</div></li>
                    <li id="game-classifica-link"><i class="fas fa-medal"></i><span>Classifica</span><div class="menu-tooltip">Classifica</div></li>
                </ul>
            </nav>
            <div class="sidebar-footer">
                <ul class="sidebar-icons">
                    <li id="game-user-profile">
                        <i class="fas fa-user"></i>
                        <div class="menu-tooltip" id="user-tooltip-text">Utente</div>
                    </li>
                    <li>
                        <i class="fas fa-globe"></i>
                        <div class="menu-tooltip">Italiano</div>
                    </li>
                    <li>
                        <i class="fas fa-question-circle"></i>
                        <div class="menu-tooltip">Supporto</div>
                    </li>
                </ul>
            </div>
        </div>

        <div id="players-column">
            <div id="player1-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot white"></div>
                        <div class="player-info-box">
                            <span class="player-name">...</span> <span class="player-rating" id="player1-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player1-avatar-container"></div>
                                <div class="player-avatar-level" id="player1-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player1-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">30:00</span>
                        </div>
                    </div>
                </div>

                <div id="player1-hand" class="hand-area">
                    <!-- Carte del giocatore 1 (bianco) -->
                </div>
            </div>

            <div id="player2-area">
                <div class="player-header">
                    <h2>
                        <div class="player-dot black"></div>
                        <div class="player-info-box">
                            <span class="player-name">...</span> <span class="player-rating" id="player2-rating"></span>
                            <div class="player-avatar-wrapper">
                                <div class="player-avatar-container" id="player2-avatar-container"></div>
                                <div class="player-avatar-level" id="player2-avatar-level"></div>
                            </div>
                        </div>
                    </h2>
                    <div id="player2-total-timer" class="total-timer-integrated">
                        <div class="hourglass">
                            <div class="hourglass-top"></div>
                            <div class="hourglass-bottom"></div>
                        </div>
                        <div class="timer-wrapper">
                            <i class="fas fa-clock"></i>
                            <span class="total-timer-count">30:00</span>
                        </div>
                    </div>
                </div>

                <div id="player2-hand" class="hand-area">
                    <!-- Carte del giocatore 2 (nero) -->
                </div>
            </div>
        </div>

        <div id="board-area" style="position: relative;">
            <div id="advantage-indicator">
                <div class="advantage-white">
                    <span class="advantage-label">G1</span>
                </div>
                <div class="advantage-black">
                    <span class="advantage-label">G2</span>
                </div>
            </div>
            <div id="game-board" style="position: relative;">
                <!-- La griglia di gioco verrà generata qui -->
                
            </div>

            <div id="board-sidebar">
                <!-- Tab container -->
                <div id="sidebar-tabs" class="sidebar-tabs">
                    <div class="tab active" data-tab="gioca">
                        <i class="fas fa-gamepad"></i>
                        <span class="tab-text">Gioca</span>
                    </div>
                    <div class="tab" data-tab="nuova-partita">
                        <i class="fas fa-play"></i>
                        <span class="tab-text">Nuova Partita</span>
                    </div>
                    <div class="tab" data-tab="analisi">
                        <i class="fas fa-chart-bar"></i>
                        <span class="tab-text">Analisi</span>
                    </div>
                    <div class="tab" data-tab="giocatori">
                        <i class="fas fa-users"></i>
                        <span class="tab-text">Giocatori</span>
                    </div>
                </div>

                <!-- Tab content containers -->
                <div id="sidebar-content">
                    <!-- Gioca tab content -->
                    <div id="tab-gioca" class="tab-content active">
                        <div id="game-message-container">
                            <p id="game-message"></p>
                        </div>

                        <div class="game-actions">
                            <h4><i class="fas fa-chess"></i> Azioni di Gioco</h4>
                            <div class="action-buttons">
                                <button id="draw-card-button" class="action-button">
                                    <i class="fas fa-hand-paper"></i>
                                    <span>Pesca Carta</span>
                                    <span id="deck-counter" class="deck-counter">39</span>
                                </button>
                                <button id="hint-button" class="action-button">
                                    <i class="fas fa-lightbulb"></i>
                                    <span>Suggerimento</span>
                                </button>
                            </div>
                        </div>

                        <div class="game-status">
                            <h4><i class="fas fa-info-circle"></i> Stato Partita</h4>
                            <div class="status-info">
                                <div class="status-row">
                                    <span class="status-label">Dadi:</span>
                                    <span class="status-value" id="dice-result">-</span>
                                </div>

                                <div class="status-row move-navigation">
                                    <span class="status-label">Mosse:</span>
                                    <div class="move-navigation-controls">
                                        <button id="prev-move-btn" class="move-nav-btn" title="Mossa precedente">
                                            <i class="fas fa-arrow-left"></i>
                                        </button>
                                        <span class="move-counter" id="current-move-counter">0/0</span>
                                        <button id="next-move-btn" class="move-nav-btn" title="Mossa successiva">
                                            <i class="fas fa-arrow-right"></i>
                                        </button>
                                        <button id="current-move-btn" class="move-nav-btn" title="Torna alla mossa corrente">
                                            <i class="fas fa-play"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Opzioni di fine partita integrate nello stato partita -->
                                <div class="status-row end-game-options-row">
                                    <span class="status-label">Opzioni:</span>
                                    <div class="end-game-buttons-integrated">
                                        <button id="offer-draw-button" class="end-game-button draw-button">
                                            <i class="fas fa-handshake"></i>
                                            <span>Chiedi Patta</span>
                                        </button>
                                        <button id="resign-button" class="end-game-button resign-button">
                                            <i class="fas fa-times-circle"></i>
                                            <span>Abbandona</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sezione Skèmino notation -->
                        <div id="skemino-notation-area">
                            <!-- PSN Visualizer sarà inizializzato qui -->
                        </div>

                        <!-- Chat per il multiplayer -->
                        <div id="chat-area" class="chat-area">
                            <div class="chat-header">
                                <i class="fas fa-comments"></i> Chat Multiplayer
                            </div>
                            <div id="chat-messages" class="chat-messages"></div>
                            <div class="chat-input-container">
                                <input type="text" id="chat-input" placeholder="Invia messaggio..." maxlength="100">
                                <button id="emoji-button" class="emoji-button">
                                    <i class="fas fa-smile"></i>
                                </button>
                            </div>
                            <div id="emoji-picker" class="emoji-picker">
                                <div class="emoji-list">
                                    <span class="emoji-item">😀</span>
                                    <span class="emoji-item">😎</span>
                                    <span class="emoji-item">👍</span>
                                    <span class="emoji-item">👏</span>
                                    <span class="emoji-item">🎮</span>
                                    <span class="emoji-item">🤔</span>
                                    <span class="emoji-item">🙂</span>
                                    <span class="emoji-item">😊</span>
                                    <span class="emoji-item">👋</span>
                                    <span class="emoji-item">🏆</span>
                                    <span class="emoji-item">⭐</span>
                                    <span class="emoji-item">❤️</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Nuova Partita tab content -->
                    <div id="tab-nuova-partita" class="tab-content">
                        <div id="game-message-container-nuova-partita">
                            <p id="game-message-nuova-partita"></p>
                        </div>

                        <div class="new-game-options">
                            <h4><i class="fas fa-play-circle"></i> Opzioni Nuova Partita</h4>
                            <div class="options-container">
                                <div class="option-row">
                                    <span class="option-label">Modalità:</span>
                                    <div class="option-value">
                                        <select id="game-mode-select" class="game-select">
                                            <option value="local">Locale (2 giocatori)</option>
                                            <option value="online">Online (Matchmaking)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="option-row">
                                    <span class="option-label">Tempo per mossa:</span>
                                    <div class="option-value">
                                        <select id="time-select" class="game-select">
                                            <option value="none">Nessun limite</option>
                                            <option value="30">30 secondi</option>
                                            <option value="60">1 minuto</option>
                                            <option value="120">2 minuti</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="start-game-button-container">
                                <button id="start-new-game-button" class="start-game-button">
                                    <i class="fas fa-play"></i>
                                    <span>Nuova Partita</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Altri tab content omessi per brevità -->
                </div>
            </div>
        </div>
    </div>

    <!-- Mazzo di carte -->
    <div class="deck-area">
        <div class="deck-label" id="deck-label">Mazzo di gioco</div>
        <div id="deck">
            <div class="card-stack-visual static-deck">
                <div class="card-pile-1"></div>
                <div class="card-pile-2"></div>
                <div class="card-pile-3"></div>
                <div class="card-pile-4"></div>
                <div class="card-pile-5"></div>
            </div>
        </div>
    </div>

    <!-- Nuova posizione: Animazione nomi giocatori multiplayer -->
    <div id="setup-animation" style="display: none !important;">
        <!-- Animazione nomi giocatori -->
        <div class="player-names-animation">
            <div id="player1-name-flying" class="player-name-flying player1"></div>
            <div id="player2-name-flying" class="player-name-flying player2"></div>
        </div>
        
        <!-- Testo centrale -->
        <div class="setup-center-text">
            <h2>Partita Online</h2>
            <p>Preparazione in corso...</p>
        </div>
    </div>

    <!-- Victory Screen -->
    <div id="victory-screen" style="display: none;">
        <div class="victory-container">
            <div class="victory-header">
                <i class="fas fa-trophy victory-icon"></i>
                <h2>Vittoria!</h2>
            </div>
            <div class="victory-content">
                <p id="winner-name">Nome Vincitore</p>
                <div id="rating-changes">
                    <p id="winner-rating" class="rating-change winner-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                    <p id="loser-rating" class="rating-change loser-rating">Rating: <span class="old-rating"></span> → <span class="new-rating"></span> <span class="rating-diff"></span></p>
                </div>
                <p id="victory-reason">Motivo della vittoria</p>
            </div>
            <div class="victory-buttons">
                <button id="examine-game-victory" class="primary-btn">
                    <i class="fas fa-search"></i> Esamina Partita
                </button>
                <button id="new-game-victory" class="secondary-btn">
                    <i class="fas fa-redo"></i> Nuova Partita
                </button>
            </div>
        </div>
    </div>

    <!-- Battle Start Overlay -->
    <div id="battle-start-overlay">
        <div class="battle-start-text"></div>
        <div class="battle-particles"></div>
    </div>

    <!-- Turn Timer -->
    <div id="turn-timer-container" class="turn-timer-container" style="display: none;">
        <div id="turn-timer" class="turn-timer">00:00</div>
    </div>

    <!-- Notifications -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Matchmaking Modal -->
    <div id="matchmaking-modal" class="matchmaking-modal">
        <div class="matchmaking-content">
            <h2>Ricerca Avversario</h2>
            <div class="matchmaking-spinner"></div>
            <p id="matchmaking-status" class="matchmaking-status">Ricerca di un avversario in corso...</p>
            <div class="matchmaking-buttons">
                <button id="cancel-matchmaking" class="cancel-matchmaking"><i class="fas fa-times-circle"></i> Annulla Ricerca</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/socket.io/socket.io.js"></script>
    <script src="auth.js"></script>
    <script src="js/token-manager.js"></script>

    <!-- Script per gestire interfaccia completa senza animazioni -->
    <script>
        // Sistema di conservazione dello stato
        const GAME_STATE_KEY = 'skemino_game_state';

        function saveGameState(state) {
            console.log('[STATE] Salvando stato:', state);
            sessionStorage.setItem(GAME_STATE_KEY, JSON.stringify(state));
        }

        function loadGameState() {
            const saved = sessionStorage.getItem(GAME_STATE_KEY);
            if (saved) {
                try {
                    const state = JSON.parse(saved);
                    console.log('[STATE] Stato caricato:', state);
                    return state;
                } catch (e) {
                    console.error('[STATE] Errore nel parsing dello stato:', e);
                    sessionStorage.removeItem(GAME_STATE_KEY);
                }
            }
            return null;
        }

        function clearGameState() {
            console.log('[STATE] Pulizia stato');
            sessionStorage.removeItem(GAME_STATE_KEY);
        }

                 // Controlla sessionStorage e parametri URL per interfaccia completa
         (function() {
             // Prima controlla sessionStorage per evitare glitch nell'URL
             let skipAnimations = false;
             let fullInterface = false;
             let fromSessionStorage = false;
             
             const sessionData = sessionStorage.getItem('skemino_full_interface');
             if (sessionData) {
                 try {
                     const parsed = JSON.parse(sessionData);
                     // Verifica che i dati non siano troppo vecchi (max 30 secondi)
                     if (Date.now() - parsed.timestamp < 30000) {
                         skipAnimations = parsed.skipAnimations || false;
                         fullInterface = parsed.fullInterface || false;
                         fromSessionStorage = true;
                         // Pulisci immediatamente per evitare riutilizzo
                         sessionStorage.removeItem('skemino_full_interface');
                         console.log('[STATE] Dati caricati da sessionStorage e rimossi');
                     } else {
                         console.log('[STATE] Dati sessionStorage scaduti, ignoro');
                         sessionStorage.removeItem('skemino_full_interface');
                     }
                 } catch (e) {
                     console.error('[STATE] Errore parsing sessionStorage:', e);
                     sessionStorage.removeItem('skemino_full_interface');
                 }
             }
             
             // Se non trovati in sessionStorage, controlla parametri URL (compatibilità)
             if (!fromSessionStorage) {
                 const urlParams = new URLSearchParams(window.location.search);
                 skipAnimations = urlParams.get('skipAnimations') === 'true';
                 fullInterface = urlParams.get('fullInterface') === 'true';
             }
             
             const savedState = loadGameState();

             console.log('[STATE] Controllo iniziale:');
             console.log('[STATE] - skipAnimations:', skipAnimations);
             console.log('[STATE] - fullInterface:', fullInterface);
             console.log('[STATE] - fromSessionStorage:', fromSessionStorage);
             console.log('[STATE] - savedState:', savedState);

             // Determina se mostrare l'interfaccia completa
             const shouldShowFullInterface = (skipAnimations && fullInterface) ||
                                           (savedState && savedState.fullInterface);

            console.log('[STATE] - shouldShowFullInterface:', shouldShowFullInterface);

            if (shouldShowFullInterface) {
                if (savedState && savedState.fullInterface) {
                    console.log('[GAME] Stato salvato rilevato: ripristino interfaccia completa');
                } else if (fromSessionStorage) {
                    console.log('[GAME] Dati da sessionStorage: attivo interfaccia completa');
                } else {
                    console.log('[GAME] Parametri URL rilevati: attivo interfaccia completa');
                }

                // Mostra immediatamente l'interfaccia completa
                document.addEventListener('DOMContentLoaded', function() {
                    console.log('[GAME] DOM caricato, mostro interfaccia completa');

                    // Nascondi elementi di animazione
                    const diceOverlay = document.getElementById('dice-animation-overlay');
                    const diceArea = document.getElementById('dice-animation-area');
                    const setupAnimation = document.getElementById('setup-animation');

                    if (diceOverlay) diceOverlay.style.display = 'none';
                    if (diceArea) diceArea.style.display = 'none';
                    if (setupAnimation) setupAnimation.style.display = 'none';

                    // Mostra il container del gioco
                    const gameContainer = document.getElementById('game-container');
                    if (gameContainer) {
                        gameContainer.style.display = 'grid';
                        gameContainer.classList.add('ready-for-play');
                        console.log('[GAME] Game container mostrato con interfaccia completa');
                    }

                    // Crea il tabellone di gioco
                    setTimeout(() => {
                        if (typeof createGameBoard === 'function') {
                            createGameBoard();
                            console.log('[GAME] Tabellone di gioco creato');
                        } else {
                            console.warn('[GAME] Funzione createGameBoard non disponibile');
                        }

                        // Crea anche gli slot per le mani dei giocatori
                        if (typeof createHandSlots === 'function') {
                            createHandSlots();
                            console.log('[GAME] Slot mani giocatori creati');
                        }
                    }, 100);

                    // Imposta nomi giocatori di default
                    const player1Name = document.querySelector('#player1-area .player-name');
                    const player2Name = document.querySelector('#player2-area .player-name');

                    if (player1Name) player1Name.textContent = 'Giocatore 1';
                    if (player2Name) player2Name.textContent = 'Giocatore 2';

                    // Salva lo stato dell'interfaccia completa
                    saveGameState({
                        fullInterface: true,
                        activeTab: 'nuova-partita',
                        timestamp: Date.now()
                    });

                    // Attiva il tab "Nuova Partita" e disabilita gli altri
                    setTimeout(() => {
                        // Rimuovi active da tutti i tab
                        document.querySelectorAll('.sidebar-tabs .tab').forEach(tab => {
                            tab.classList.remove('active');
                            // Disabilita tutti i tab tranne "nuova-partita"
                            if (tab.getAttribute('data-tab') !== 'nuova-partita') {
                                tab.style.pointerEvents = 'none';
                                tab.style.opacity = '0.5';
                                tab.style.cursor = 'not-allowed';
                            }
                        });

                        // Nascondi tutti i contenuti dei tab
                        document.querySelectorAll('.tab-content').forEach(content => {
                            content.classList.remove('active');
                        });

                        // Attiva solo il tab "Nuova Partita"
                        const nuovaPartitaTab = document.querySelector('.tab[data-tab="nuova-partita"]');
                        const nuovaPartitaContent = document.getElementById('tab-nuova-partita');

                        if (nuovaPartitaTab) {
                            nuovaPartitaTab.classList.add('active');
                            console.log('[GAME] Tab "Nuova Partita" attivato');
                        }

                        if (nuovaPartitaContent) {
                            nuovaPartitaContent.classList.add('active');
                            console.log('[GAME] Contenuto "Nuova Partita" mostrato');
                        }

                        // Aggiungi event listener al pulsante "Nuova Partita"
                        const startNewGameButton = document.getElementById('start-new-game-button');
                        if (startNewGameButton) {
                            startNewGameButton.addEventListener('click', function() {
                                console.log('[GAME] Pulsante Nuova Partita cliccato');

                                // Ottieni le opzioni selezionate
                                const gameMode = document.getElementById('game-mode-select')?.value || 'local';
                                const timeLimit = document.getElementById('time-select')?.value || 'none';

                                console.log('[GAME] Modalità:', gameMode, 'Tempo:', timeLimit);

                                if (gameMode === 'online') {
                                    // Avvia matchmaking online
                                    startOnlineMatchmaking();
                                } else {
                                    // Avvia partita locale
                                    const messageElement = document.getElementById('game-message-nuova-partita');
                                    if (messageElement) {
                                        messageElement.textContent = `Avvio partita locale con tempo ${timeLimit}...`;
                                        messageElement.style.color = '#4CAF50';
                                    }

                                    // TODO: Implementare logica partita locale
                                    // Qui si potrebbe chiamare una funzione per iniziare la partita locale
                                    // o mostrare la schermata di inserimento nomi giocatori
                                }
                            });
                        }

                        // Funzione per avviare il matchmaking online
                        function startOnlineMatchmaking() {
                            console.log('[MATCHMAKING] Avvio matchmaking online');

                            // Verifica se l'utente è autenticato
                            if (!localStorage.getItem('loggedInUser') && (!window.authUtils || !window.authUtils.isLoggedIn())) {
                                const messageElement = document.getElementById('game-message-nuova-partita');
                                if (messageElement) {
                                    messageElement.textContent = 'Devi essere loggato per giocare online';
                                    messageElement.style.color = '#f44336';
                                }
                                return;
                            }

                            // Prima di mostrare il modal, verifica che il socket sia connesso
                            if (!window.socket || !window.socket.connected) {
                                if (typeof initializeSocket === 'function') {
                                    console.log('[MATCHMAKING] Socket non connesso, inizializzazione...');
                                    initializeSocket();
                                }
                            } else {
                                console.log('[MATCHMAKING] Socket già connesso');

                                // Verifica che il socket abbia il token di autenticazione
                                const token = localStorage.getItem('token');
                                if (token && (!window.socket.auth || !window.socket.auth.token)) {
                                    console.log('[MATCHMAKING] Socket senza token, aggiornamento auth...');
                                    window.socket.auth = { token: token };
                                    window.socket.disconnect();
                                    window.socket.connect();
                                }
                            }

                            // Mostra il modal di matchmaking
                            const matchmakingModal = document.getElementById('matchmaking-modal');
                            if (matchmakingModal) {
                                matchmakingModal.style.display = 'flex';

                                // Attendi un momento per la connessione del socket
                                setTimeout(() => {
                                    if (window.socket && window.socket.connected) {
                                        console.log('[MATCHMAKING] Socket connesso, invio findMatch');

                                        // Rimuovi eventuali handler precedenti e aggiungi il nuovo
                                        window.socket.off('matchFound');
                                        window.socket.on('matchFound', (data) => {
                                            console.log('[MATCHMAKING] Match trovato!', data);

                                            // Chiudi il modal
                                            matchmakingModal.style.display = 'none';

                                            // Salva i dati della partita
                                            if (data.gameId) {
                                                console.log('[MATCHMAKING] Salvando dati partita con gameId:', data.gameId);
                                                sessionStorage.setItem('gameData', JSON.stringify(data));

                                                // Ricarica la pagina per iniziare la partita online
                                                setTimeout(() => {
                                                    console.log('[MATCHMAKING] Ricaricando pagina per partita online');
                                                    window.location.reload();
                                                }, 1000);
                                            } else {
                                                console.error('[MATCHMAKING] Nessun gameId ricevuto');
                                            }
                                        });

                                        const userData = window.authUtils ? window.authUtils.getCurrentUser() : null;
                                        const requestData = {
                                            rating: userData?.rating || 1000,
                                            username: userData?.username || 'Giocatore'
                                        };

                                        console.log('[MATCHMAKING] Invio findMatch con dati:', requestData);
                                        window.socket.emit('findMatch', requestData);
                                    } else {
                                        console.error('[MATCHMAKING] Socket non connesso');
                                        matchmakingModal.style.display = 'none';
                                        const messageElement = document.getElementById('game-message-nuova-partita');
                                        if (messageElement) {
                                            messageElement.textContent = 'Errore di connessione al server';
                                            messageElement.style.color = '#f44336';
                                        }
                                    }
                                }, 500);
                            }
                        }

                        // Gestione del pulsante di cancellazione del matchmaking
                        const cancelMatchmakingButton = document.getElementById('cancel-matchmaking');
                        if (cancelMatchmakingButton) {
                            cancelMatchmakingButton.addEventListener('click', function() {
                                const matchmakingModal = document.getElementById('matchmaking-modal');
                                if (matchmakingModal) {
                                    matchmakingModal.style.display = 'none';
                                }

                                // Cancella la ricerca se il socket è connesso
                                if (window.socket && window.socket.connected) {
                                    window.socket.emit('cancelMatchmaking');
                                }
                            });
                        }

                        // Aggiungi listener per salvare le opzioni selezionate
                        const gameModeSelect = document.getElementById('game-mode-select');
                        const timeSelect = document.getElementById('time-select');

                        if (gameModeSelect) {
                            gameModeSelect.addEventListener('change', function() {
                                const currentState = loadGameState();
                                if (currentState) {
                                    currentState.gameMode = this.value;
                                    saveGameState(currentState);
                                }
                            });
                        }

                        if (timeSelect) {
                            timeSelect.addEventListener('change', function() {
                                const currentState = loadGameState();
                                if (currentState) {
                                    currentState.timeLimit = this.value;
                                    saveGameState(currentState);
                                }
                            });
                        }

                        // Ripristina le opzioni salvate se esistono
                        const currentState = loadGameState();
                        if (currentState) {
                            if (currentState.gameMode && gameModeSelect) {
                                gameModeSelect.value = currentState.gameMode;
                            }
                            if (currentState.timeLimit && timeSelect) {
                                timeSelect.value = currentState.timeLimit;
                            }
                        }
                    }, 200);

                                         // Pulisci l'URL dai parametri solo se presenti nell'URL
                     if (!fromSessionStorage && (skipAnimations && fullInterface)) {
                         console.log('[STATE] Pulizia parametri URL');
                         window.history.replaceState({}, document.title, '/game');
                     } else if (fromSessionStorage) {
                         console.log('[STATE] Dati da sessionStorage, URL già pulito');
                     }
                });
            } else {
                // Se non siamo in modalità interfaccia completa E non c'è uno stato salvato valido
                const currentState = loadGameState();
                if (!currentState || !currentState.fullInterface) {
                    console.log('[STATE] Nessuna interfaccia completa richiesta, pulizia stato');
                    clearGameState();
                } else {
                    console.log('[STATE] Stato salvato presente ma condizioni non soddisfatte per il ripristino');
                }
            }

            // Listener per pulire lo stato quando si naviga via dalla pagina
            window.addEventListener('beforeunload', function() {
                // Non pulire lo stato se stiamo ricaricando la pagina
                // Lo stato verrà pulito solo quando si naviga effettivamente via
                const savedState = loadGameState();
                if (savedState && savedState.fullInterface) {
                    // Mantieni lo stato per il reload
                    console.log('[STATE] Mantenendo stato per reload');
                } else {
                    clearGameState();
                }
            });

            // Listener per pulire lo stato quando si inizia una partita normale
            window.addEventListener('gameStarted', function() {
                console.log('[STATE] Partita iniziata, pulizia stato');
                clearGameState();
            });
        })();
    </script>

    <script src="script.js"></script>
    <script src="js/multiplayer.js"></script>
    <script src="js/psn-unified.js"></script>
    <script src="js/page-transitions.js"></script>
    <!-- Game mode managers -->
    <script src="js/local-game.js"></script>
    <script src="js/online-game.js"></script>
    <script src="js/game-mode-manager.js"></script>
    <script src="js/online-play-enhancer.js"></script>
    <script src="js/player-names-protection.js"></script>
    <script src="js/smooth-loading.js"></script>
    <script src="victory-fix.js"></script>
</body>
</html>